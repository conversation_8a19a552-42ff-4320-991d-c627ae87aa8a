const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Schema for individual CO attainment calculation
const CoAttainmentSchema = new Schema({
    coIdentifier: { type: String, required: true }, // e.g., "CO1", "CO2"
    directAssessment: {
        totalMarks: { type: Number, default: 0 },
        obtainedMarks: { type: Number, default: 0 },
        percentage: { type: Number, default: 0 },
        attainmentLevel: { type: Number, default: 0 } // 0, 1, 2, or 3
    },
    indirectAssessment: {
        totalMarks: { type: Number, default: 0 },
        obtainedMarks: { type: Number, default: 0 },
        percentage: { type: Number, default: 0 },
        attainmentLevel: { type: Number, default: 0 }
    },
    overallAttainment: {
        weightedPercentage: { type: Number, default: 0 }, // 80% DA + 20% IA
        attainmentLevel: { type: Number, default: 0 },
        isAttained: { type: Boolean, default: false } // Based on threshold (usually 60%)
    }
}, { _id: false });

// Schema for PO attainment calculation
const PoAttainmentSchema = new Schema({
    poIdentifier: { type: String, required: true }, // e.g., "PO1", "PO2", "PSO1"
    mappedCOs: [{
        coIdentifier: String,
        mappingStrength: { type: Number, min: 0, max: 3 }, // 1=Low, 2=Medium, 3=High
        coAttainment: Number, // CO attainment level (0-3)
        weightedContribution: Number // mappingStrength * coAttainment
    }],
    totalWeightedSum: { type: Number, default: 0 },
    totalMappingStrength: { type: Number, default: 0 },
    attainmentPercentage: { type: Number, default: 0 },
    attainmentLevel: { type: Number, default: 0 },
    isAttained: { type: Boolean, default: false }
}, { _id: false });

// Schema for assessment data aggregation
const AssessmentDataSchema = new Schema({
    assessmentType: { type: String, enum: ['tms', 'tca', 'tes'], required: true },
    assessmentDetails: {
        tmsType: String, // For TMS: Tutorial, MiniProject, SurpriseTest
        assessmentNumber: Number, // For TCA: 1 or 2
        totalStudents: Number,
        studentsAppeared: Number
    },
    coWiseData: [{
        coIdentifier: String,
        totalMarks: Number,
        studentsData: [{
            rollNo: String,
            name: String,
            marksObtained: Number,
            percentage: Number
        }],
        statistics: {
            averageMarks: Number,
            averagePercentage: Number,
            studentsAboveThreshold: Number, // Students scoring > 60%
            attainmentPercentage: Number // (studentsAboveThreshold / totalStudents) * 100
        }
    }]
}, { _id: false });

// Main CO-PO Assessment Schema
const CoPoAssessmentSchema = new Schema({
    facultyId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    subjectCode: { type: String, required: true, index: true },
    subjectName: { type: String, required: true },
    academicYear: { type: String, required: true, index: true },
    semester: { type: Number, required: true, index: true },
    branch: { type: String, required: true, index: true },
    section: { type: String, required: true, index: true },
    
    // Assessment configuration
    assessmentConfig: {
        directAssessmentWeight: { type: Number, default: 80 }, // Usually 80%
        indirectAssessmentWeight: { type: Number, default: 20 }, // Usually 20%
        attainmentThreshold: { type: Number, default: 60 }, // Usually 60%
        coThresholds: {
            level1: { type: Number, default: 40 }, // 40-50%
            level2: { type: Number, default: 60 }, // 60-70%
            level3: { type: Number, default: 80 }  // 80%+
        }
    },
    
    // Raw assessment data
    assessmentData: [AssessmentDataSchema],
    
    // Calculated CO attainments
    coAttainments: [CoAttainmentSchema],
    
    // Calculated PO attainments
    poAttainments: [PoAttainmentSchema],
    
    // Overall statistics
    overallStatistics: {
        totalCOs: { type: Number, default: 0 },
        attainedCOs: { type: Number, default: 0 },
        coAttainmentPercentage: { type: Number, default: 0 },
        totalPOs: { type: Number, default: 0 },
        attainedPOs: { type: Number, default: 0 },
        poAttainmentPercentage: { type: Number, default: 0 }
    },
    
    // Calculation metadata
    calculationMetadata: {
        lastCalculated: { type: Date, default: Date.now },
        calculationMethod: { type: String, default: 'standard' }, // standard, weighted, custom
        dataSource: [String], // Array of assessment IDs used for calculation
        isFinalized: { type: Boolean, default: false }
    },
    
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
});

// Middleware to update timestamp
CoPoAssessmentSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

// Compound index for efficient querying
CoPoAssessmentSchema.index({ 
    subjectCode: 1, 
    academicYear: 1, 
    semester: 1, 
    branch: 1, 
    section: 1 
}, { unique: true });

module.exports = mongoose.model('CoPoAssessment', CoPoAssessmentSchema);
