import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from "../../ui/card";
import { But<PERSON> } from "../../ui/button";
import { Alert, AlertDescription } from "../../ui/alert";
import { Badge } from "../../ui/badge";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "../../ui/tabs";
import {
    Plus,
    Eye,
    Edit,
    Trash2,
    FileText,
    Calculator,
    Download,
    Upload
} from 'lucide-react';
import api from '../../../utils/api';
import CreateAssessmentModal from './CreateAssessmentModal';

const AssessmentManager = ({ facultyId, classData }) => {
    const [assessments, setAssessments] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [selectedAssessment, setSelectedAssessment] = useState(null);
    const [activeTab, setActiveTab] = useState('list');
    const [showCreateModal, setShowCreateModal] = useState(false);

    useEffect(() => {
        if (facultyId && classData) {
            fetchAssessments();
        }
    }, [facultyId, classData]);

    const fetchAssessments = async () => {
        try {
            setLoading(true);
            setError(null);

            // Fetch assessments for the faculty and class
            const response = await api.get(`/api/assessments/faculty/${facultyId}`, {
                params: {
                    academicYear: classData.academicYear,
                    semester: classData.semester,
                    branch: classData.branch,
                    section: classData.section
                }
            });

            setAssessments(response.data || []);
        } catch (err) {
            setError('Failed to fetch assessments');
            console.error('Error fetching assessments:', err);
        } finally {
            setLoading(false);
        }
    };

    const getAssessmentTypeBadge = (type) => {
        const types = {
            'tms': { label: 'TMS', variant: 'default', color: 'bg-blue-100 text-blue-800' },
            'tca': { label: 'TCA', variant: 'secondary', color: 'bg-green-100 text-green-800' },
            'tes': { label: 'TES', variant: 'destructive', color: 'bg-red-100 text-red-800' }
        };
        
        const typeInfo = types[type] || types['tms'];
        return (
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${typeInfo.color}`}>
                {typeInfo.label}
            </span>
        );
    };

    const getAssessmentSubtitle = (assessment) => {
        if (assessment.type === 'tms') {
            return assessment.tmsType || 'TMS';
        } else if (assessment.type === 'tca') {
            return `TCA ${assessment.assessmentNumber || ''}`;
        } else if (assessment.type === 'tes') {
            return 'TES';
        }
        return assessment.type.toUpperCase();
    };

    const handleCreateAssessment = () => {
        setShowCreateModal(true);
    };

    const handleAssessmentCreated = (newAssessment) => {
        // Refresh the assessments list
        fetchAssessments();
        setShowCreateModal(false);
    };

    const handleViewAssessment = (assessment) => {
        setSelectedAssessment(assessment);
        setActiveTab('view');
    };

    const handleEditAssessment = (assessment) => {
        // Navigate to edit form or open edit modal
        console.log('Edit assessment:', assessment._id);
    };

    const handleDeleteAssessment = async (assessmentId) => {
        if (!confirm('Are you sure you want to delete this assessment?')) {
            return;
        }

        try {
            await api.delete(`/api/assessments/${assessmentId}`);
            await fetchAssessments(); // Refresh the list
        } catch (err) {
            setError('Failed to delete assessment');
            console.error('Error deleting assessment:', err);
        }
    };

    const renderAssessmentList = () => (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Assessment Data</h3>
                <Button onClick={handleCreateAssessment} className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Create Assessment
                </Button>
            </div>

            {loading && (
                <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-2 text-gray-600">Loading assessments...</p>
                </div>
            )}

            {error && (
                <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {!loading && assessments.length === 0 && (
                <Card>
                    <CardContent className="text-center py-12">
                        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-semibold text-gray-700 mb-2">No Assessments Found</h3>
                        <p className="text-gray-600 mb-4">
                            Create assessments to start CO-PO analysis
                        </p>
                        <Button onClick={handleCreateAssessment}>
                            Create First Assessment
                        </Button>
                    </CardContent>
                </Card>
            )}

            {!loading && assessments.length > 0 && (
                <div className="grid gap-4">
                    {assessments.map((assessment) => (
                        <Card key={assessment._id} className="hover:shadow-md transition-shadow">
                            <CardContent className="p-4">
                                <div className="flex justify-between items-start">
                                    <div className="flex-1">
                                        <div className="flex items-center gap-3 mb-2">
                                            {getAssessmentTypeBadge(assessment.type)}
                                            <h4 className="font-semibold">
                                                {assessment.subject?.name || 'Unknown Subject'}
                                            </h4>
                                            <span className="text-sm text-gray-500">
                                                ({assessment.subject?.code})
                                            </span>
                                        </div>
                                        
                                        <div className="text-sm text-gray-600 space-y-1">
                                            <div>
                                                <span className="font-medium">Type:</span> {getAssessmentSubtitle(assessment)}
                                            </div>
                                            <div>
                                                <span className="font-medium">Students:</span> {assessment.numberOfStudents || 0}
                                            </div>
                                            <div>
                                                <span className="font-medium">Created:</span> {new Date(assessment.createdAt).toLocaleDateString()}
                                            </div>
                                            {assessment.academicYear && (
                                                <div>
                                                    <span className="font-medium">Academic Year:</span> {assessment.academicYear}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                    
                                    <div className="flex items-center gap-2">
                                        <Button
                                            size="sm"
                                            variant="outline"
                                            onClick={() => handleViewAssessment(assessment)}
                                            className="flex items-center gap-1"
                                        >
                                            <Eye className="h-3 w-3" />
                                            View
                                        </Button>
                                        <Button
                                            size="sm"
                                            variant="outline"
                                            onClick={() => handleEditAssessment(assessment)}
                                            className="flex items-center gap-1"
                                        >
                                            <Edit className="h-3 w-3" />
                                            Edit
                                        </Button>
                                        <Button
                                            size="sm"
                                            variant="outline"
                                            onClick={() => handleDeleteAssessment(assessment._id)}
                                            className="flex items-center gap-1 text-red-600 hover:text-red-700"
                                        >
                                            <Trash2 className="h-3 w-3" />
                                            Delete
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            )}
        </div>
    );

    const renderAssessmentView = () => {
        if (!selectedAssessment) {
            return (
                <div className="text-center py-8">
                    <p className="text-gray-600">Select an assessment to view details</p>
                </div>
            );
        }

        return (
            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Button 
                        variant="outline" 
                        onClick={() => setActiveTab('list')}
                        className="flex items-center gap-2"
                    >
                        ← Back to List
                    </Button>
                    <h3 className="text-lg font-semibold">Assessment Details</h3>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-3">
                            {getAssessmentTypeBadge(selectedAssessment.type)}
                            {selectedAssessment.subject?.name} - {getAssessmentSubtitle(selectedAssessment)}
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 className="font-semibold mb-3">Assessment Information</h4>
                                <div className="space-y-2 text-sm">
                                    <div><span className="font-medium">Subject:</span> {selectedAssessment.subject?.name} ({selectedAssessment.subject?.code})</div>
                                    <div><span className="font-medium">Type:</span> {getAssessmentSubtitle(selectedAssessment)}</div>
                                    <div><span className="font-medium">Academic Year:</span> {selectedAssessment.academicYear}</div>
                                    <div><span className="font-medium">Semester:</span> {selectedAssessment.semester}</div>
                                    <div><span className="font-medium">Branch:</span> {selectedAssessment.branch}</div>
                                    <div><span className="font-medium">Section:</span> {selectedAssessment.section}</div>
                                </div>
                            </div>
                            
                            <div>
                                <h4 className="font-semibold mb-3">Statistics</h4>
                                <div className="space-y-2 text-sm">
                                    <div><span className="font-medium">Total Students:</span> {selectedAssessment.numberOfStudents || 0}</div>
                                    <div><span className="font-medium">Students with Data:</span> {selectedAssessment.students?.length || 0}</div>
                                    <div><span className="font-medium">Created:</span> {new Date(selectedAssessment.createdAt).toLocaleDateString()}</div>
                                    <div><span className="font-medium">Last Updated:</span> {new Date(selectedAssessment.updatedAt).toLocaleDateString()}</div>
                                </div>
                            </div>
                        </div>

                        <div className="mt-6">
                            <h4 className="font-semibold mb-3">Actions</h4>
                            <div className="flex gap-2">
                                <Button variant="outline" className="flex items-center gap-2">
                                    <Calculator className="h-4 w-4" />
                                    Use for CO-PO Calculation
                                </Button>
                                <Button variant="outline" className="flex items-center gap-2">
                                    <Download className="h-4 w-4" />
                                    Export Data
                                </Button>
                                <Button variant="outline" className="flex items-center gap-2">
                                    <Edit className="h-4 w-4" />
                                    Edit Assessment
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        );
    };

    const renderQuickActions = () => (
        <div className="space-y-4">
            <h3 className="text-lg font-semibold">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={handleCreateAssessment}>
                    <div className="flex items-center gap-3">
                        <Plus className="h-8 w-8 text-blue-600" />
                        <div>
                            <h4 className="font-medium">Create New Assessment</h4>
                            <p className="text-sm text-gray-600">Add TMS, TCA, or TES assessment data</p>
                        </div>
                    </div>
                </Card>
                
                <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer">
                    <div className="flex items-center gap-3">
                        <Upload className="h-8 w-8 text-green-600" />
                        <div>
                            <h4 className="font-medium">Import Assessment Data</h4>
                            <p className="text-sm text-gray-600">Upload assessment data from Excel/CSV</p>
                        </div>
                    </div>
                </Card>
            </div>
        </div>
    );

    if (!classData) {
        return (
            <Card>
                <CardContent className="text-center py-8">
                    <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">Class data not available. Please ensure you are assigned to a class.</p>
                </CardContent>
            </Card>
        );
    }

    return (
        <div className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="list">Assessment List</TabsTrigger>
                    <TabsTrigger value="view">View Details</TabsTrigger>
                    <TabsTrigger value="actions">Quick Actions</TabsTrigger>
                </TabsList>

                <TabsContent value="list" className="space-y-4">
                    {renderAssessmentList()}
                </TabsContent>

                <TabsContent value="view" className="space-y-4">
                    {renderAssessmentView()}
                </TabsContent>

                <TabsContent value="actions" className="space-y-4">
                    {renderQuickActions()}
                </TabsContent>
            </Tabs>

            {/* Create Assessment Modal */}
            <CreateAssessmentModal
                isOpen={showCreateModal}
                onClose={() => setShowCreateModal(false)}
                facultyId={facultyId}
                classData={classData}
                onAssessmentCreated={handleAssessmentCreated}
            />
        </div>
    );
};

export default AssessmentManager;
