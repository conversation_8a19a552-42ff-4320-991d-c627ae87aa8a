const express = require('express');
const router = express.Router();
const cors = require('cors');
const mongoose = require('mongoose');
const CoPoAssessment = require('../models/CoPoAssessment');
const copoCalculationService = require('../services/copoCalculationService');
const copoAnalysisEngine = require('../services/copoAnalysisEngine');

// Configure CORS
const corsOptions = {
    origin: process.env.FRONTEND_URL || 'http://localhost:5173',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
    optionsSuccessStatus: 200
};

router.use(cors(corsOptions));

/**
 * POST /api/copo-assessment/calculate
 * Calculate CO-PO assessment for a subject
 */
router.post('/calculate', async (req, res) => {
    try {
        const { facultyId, subjectCode, academicYear, semester, branch, section } = req.body;

        // Validation
        if (!facultyId || !subjectCode || !academicYear || !semester || !branch || !section) {
            return res.status(400).json({
                error: 'Missing required parameters',
                required: ['facultyId', 'subjectCode', 'academicYear', 'semester', 'branch', 'section']
            });
        }

        if (!mongoose.Types.ObjectId.isValid(facultyId)) {
            return res.status(400).json({ error: 'Invalid Faculty ID format' });
        }

        console.log('Calculating CO-PO assessment for:', {
            facultyId, subjectCode, academicYear, semester, branch, section
        });

        // Perform calculation
        const result = await copoCalculationService.calculateCoPoAssessment(
            facultyId, subjectCode, academicYear, parseInt(semester), branch, section
        );

        res.status(200).json({
            message: 'CO-PO assessment calculated successfully',
            data: result
        });

    } catch (error) {
        console.error('Error calculating CO-PO assessment:', error);
        res.status(500).json({
            error: 'Failed to calculate CO-PO assessment',
            details: error.message
        });
    }
});

/**
 * GET /api/copo-assessment/:facultyId
 * Get CO-PO assessment data for a faculty's subject
 */
router.get('/:facultyId', async (req, res) => {
    try {
        const { facultyId } = req.params;
        const { subjectCode, academicYear, semester, branch, section } = req.query;

        // Validation
        if (!mongoose.Types.ObjectId.isValid(facultyId)) {
            return res.status(400).json({ error: 'Invalid Faculty ID format' });
        }

        if (!subjectCode || !academicYear || !semester || !branch || !section) {
            return res.status(400).json({
                error: 'Missing required query parameters',
                required: ['subjectCode', 'academicYear', 'semester', 'branch', 'section']
            });
        }

        // Build query
        const query = {
            facultyId: facultyId,
            subjectCode: subjectCode,
            academicYear: academicYear,
            semester: parseInt(semester),
            branch: branch,
            section: section
        };

        const assessment = await CoPoAssessment.findOne(query).lean();

        if (!assessment) {
            return res.status(404).json({
                message: 'CO-PO assessment not found',
                suggestion: 'Please calculate the assessment first'
            });
        }

        res.status(200).json(assessment);

    } catch (error) {
        console.error('Error fetching CO-PO assessment:', error);
        res.status(500).json({
            error: 'Failed to fetch CO-PO assessment',
            details: error.message
        });
    }
});

/**
 * GET /api/copo-assessment/correlation-matrix/:facultyId
 * Get CO-PO correlation matrix
 */
router.get('/correlation-matrix/:facultyId', async (req, res) => {
    try {
        const { facultyId } = req.params;
        const { subjectCode, academicYear, semester, branch, section } = req.query;

        // Validation
        if (!mongoose.Types.ObjectId.isValid(facultyId)) {
            return res.status(400).json({ error: 'Invalid Faculty ID format' });
        }

        if (!subjectCode || !academicYear || !semester || !branch || !section) {
            return res.status(400).json({
                error: 'Missing required query parameters',
                required: ['subjectCode', 'academicYear', 'semester', 'branch', 'section']
            });
        }

        // Get assessment data
        const query = {
            facultyId: facultyId,
            subjectCode: subjectCode,
            academicYear: academicYear,
            semester: parseInt(semester),
            branch: branch,
            section: section
        };

        const assessment = await CoPoAssessment.findOne(query).lean();

        if (!assessment) {
            return res.status(404).json({
                message: 'CO-PO assessment not found',
                suggestion: 'Please calculate the assessment first'
            });
        }

        // Get CO-PO mapping
        const CoPoMapping = require('../models/CoPoMapping');
        const coPoMapping = await CoPoMapping.findOne({
            subjectCode: subjectCode,
            academicYear: academicYear,
            semester: parseInt(semester),
            branch: branch,
            section: section
        });

        if (!coPoMapping) {
            return res.status(404).json({
                message: 'CO-PO mapping not found',
                suggestion: 'Please create CO-PO mapping first'
            });
        }

        // Generate correlation matrix
        const matrix = copoCalculationService.generateCorrelationMatrix(
            assessment.coAttainments,
            assessment.poAttainments,
            coPoMapping
        );

        res.status(200).json({
            message: 'Correlation matrix generated successfully',
            data: matrix,
            metadata: {
                subjectCode: assessment.subjectCode,
                subjectName: assessment.subjectName,
                academicYear: assessment.academicYear,
                semester: assessment.semester,
                branch: assessment.branch,
                section: assessment.section,
                lastCalculated: assessment.calculationMetadata.lastCalculated
            }
        });

    } catch (error) {
        console.error('Error generating correlation matrix:', error);
        res.status(500).json({
            error: 'Failed to generate correlation matrix',
            details: error.message
        });
    }
});

/**
 * GET /api/copo-assessment/summary/:facultyId
 * Get CO-PO assessment summary
 */
router.get('/summary/:facultyId', async (req, res) => {
    try {
        const { facultyId } = req.params;
        const { academicYear, semester, branch, section } = req.query;

        // Validation
        if (!mongoose.Types.ObjectId.isValid(facultyId)) {
            return res.status(400).json({ error: 'Invalid Faculty ID format' });
        }

        // Build query - get all subjects for the faculty in the specified class
        const query = { facultyId: facultyId };
        if (academicYear) query.academicYear = academicYear;
        if (semester) query.semester = parseInt(semester);
        if (branch) query.branch = branch;
        if (section) query.section = section;

        const assessments = await CoPoAssessment.find(query).lean();

        if (assessments.length === 0) {
            return res.status(404).json({
                message: 'No CO-PO assessments found',
                suggestion: 'Please calculate assessments first'
            });
        }

        // Generate summary
        const summary = {
            totalSubjects: assessments.length,
            overallStatistics: {
                totalCOs: 0,
                attainedCOs: 0,
                totalPOs: 0,
                attainedPOs: 0
            },
            subjectWiseSummary: assessments.map(assessment => ({
                subjectCode: assessment.subjectCode,
                subjectName: assessment.subjectName,
                coAttainmentPercentage: assessment.overallStatistics.coAttainmentPercentage,
                poAttainmentPercentage: assessment.overallStatistics.poAttainmentPercentage,
                lastCalculated: assessment.calculationMetadata.lastCalculated,
                isFinalized: assessment.calculationMetadata.isFinalized
            }))
        };

        // Calculate overall statistics
        assessments.forEach(assessment => {
            summary.overallStatistics.totalCOs += assessment.overallStatistics.totalCOs;
            summary.overallStatistics.attainedCOs += assessment.overallStatistics.attainedCOs;
            summary.overallStatistics.totalPOs += assessment.overallStatistics.totalPOs;
            summary.overallStatistics.attainedPOs += assessment.overallStatistics.attainedPOs;
        });

        summary.overallStatistics.coAttainmentPercentage = summary.overallStatistics.totalCOs > 0 
            ? (summary.overallStatistics.attainedCOs / summary.overallStatistics.totalCOs) * 100 
            : 0;
        
        summary.overallStatistics.poAttainmentPercentage = summary.overallStatistics.totalPOs > 0 
            ? (summary.overallStatistics.attainedPOs / summary.overallStatistics.totalPOs) * 100 
            : 0;

        res.status(200).json({
            message: 'CO-PO assessment summary generated successfully',
            data: summary
        });

    } catch (error) {
        console.error('Error generating assessment summary:', error);
        res.status(500).json({
            error: 'Failed to generate assessment summary',
            details: error.message
        });
    }
});

/**
 * PUT /api/copo-assessment/finalize/:id
 * Finalize CO-PO assessment
 */
router.put('/finalize/:id', async (req, res) => {
    try {
        const { id } = req.params;

        if (!mongoose.Types.ObjectId.isValid(id)) {
            return res.status(400).json({ error: 'Invalid assessment ID format' });
        }

        const assessment = await CoPoAssessment.findByIdAndUpdate(
            id,
            { 
                'calculationMetadata.isFinalized': true,
                updatedAt: new Date()
            },
            { new: true }
        );

        if (!assessment) {
            return res.status(404).json({ message: 'CO-PO assessment not found' });
        }

        res.status(200).json({
            message: 'CO-PO assessment finalized successfully',
            data: assessment
        });

    } catch (error) {
        console.error('Error finalizing assessment:', error);
        res.status(500).json({
            error: 'Failed to finalize assessment',
            details: error.message
        });
    }
});

/**
 * GET /api/copo-assessment/analysis/:facultyId
 * Generate comprehensive CO-PO analysis report
 */
router.get('/analysis/:facultyId', async (req, res) => {
    try {
        const { facultyId } = req.params;
        const filters = req.query;

        // Validation
        if (!mongoose.Types.ObjectId.isValid(facultyId)) {
            return res.status(400).json({ error: 'Invalid Faculty ID format' });
        }

        console.log('Generating CO-PO analysis for faculty:', facultyId, 'with filters:', filters);

        // Generate analysis report
        const report = await copoAnalysisEngine.generateAnalysisReport(facultyId, filters);

        res.status(200).json({
            message: 'CO-PO analysis report generated successfully',
            data: report
        });

    } catch (error) {
        console.error('Error generating analysis report:', error);
        res.status(500).json({
            error: 'Failed to generate analysis report',
            details: error.message
        });
    }
});

/**
 * DELETE /api/copo-assessment/:id
 * Delete CO-PO assessment
 */
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;

        if (!mongoose.Types.ObjectId.isValid(id)) {
            return res.status(400).json({ error: 'Invalid assessment ID format' });
        }

        const assessment = await CoPoAssessment.findByIdAndDelete(id);

        if (!assessment) {
            return res.status(404).json({ message: 'CO-PO assessment not found' });
        }

        res.status(200).json({
            message: 'CO-PO assessment deleted successfully',
            deletedAssessment: {
                id: assessment._id,
                subjectCode: assessment.subjectCode,
                subjectName: assessment.subjectName
            }
        });

    } catch (error) {
        console.error('Error deleting assessment:', error);
        res.status(500).json({
            error: 'Failed to delete assessment',
            details: error.message
        });
    }
});

module.exports = router;
