import React, { useState, useEffect } from 'react';
import { useAssessment } from '../../../hooks/useAssessment';
import CoPoAssessmentDashboard from '../../../Components/Faculty/CopoAssessment/CoPoAssessmentDashboard';
import { Alert, AlertDescription } from "../../../Components/ui/alert";
import { Card, CardContent } from "../../../Components/ui/card";
import { BookOpen } from 'lucide-react';
import { fetchUserData } from '../../../utils/auth';

const CoAnalysis = () => {
  const [user, setUser] = useState(null);
  const [userLoading, setUserLoading] = useState(true);
  const { classData, loading: classLoading, error: classError } = useAssessment();
  const [error, setError] = useState(null);

  useEffect(() => {
    const getUserData = async () => {
      try {
        const userData = await fetchUserData();
        setUser(userData);
      } catch (err) {
        setError('Failed to load user data');
      } finally {
        setUserLoading(false);
      }
    };
    getUserData();
  }, []);

  useEffect(() => {
    if (classError) {
      setError('Failed to load class data. Please ensure you have been assigned to a class.');
    }
  }, [classError]);

  if (classLoading || userLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading CO-PO Assessment System...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !classData) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          <h1 className="text-2xl font-bold mb-6">CO-PO Assessment System</h1>

          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Card>
            <CardContent className="text-center py-12">
              <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-700 mb-2">
                Class Data Not Available
              </h3>
              <p className="text-gray-600 mb-4">
                Please ensure you have been assigned to a class and have the necessary permissions
                to access the CO-PO assessment system.
              </p>
              <p className="text-sm text-gray-500">
                Contact your administrator if this issue persists.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900">CO-PO Assessment System</h1>
          <p className="text-gray-600 mt-2">
            Comprehensive Course Outcome and Program Outcome assessment and analysis
          </p>
        </div>

        <CoPoAssessmentDashboard
          facultyId={user?._id}
          classData={classData}
        />
      </div>
    </div>
  );
};

export default CoAnalysis;