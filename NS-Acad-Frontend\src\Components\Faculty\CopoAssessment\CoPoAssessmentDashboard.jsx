import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from "../../ui/card";
import { <PERSON><PERSON> } from "../../ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../../ui/tabs";
import { Alert, AlertDescription } from "../../ui/alert";
import { Badge } from "../../ui/badge";
import { 
    Calculator, 
    Grid, 
    TrendingUp, 
    FileText, 
    Download,
    BookOpen,
    Target,
    BarChart3
} from 'lucide-react';

// Import the components we created
import CoPoCalculation from './CoPoCalculation';
import CoPoCorrelationMatrix from './CoPoCorrelationMatrix';
import CoPoMappingTable from './CoPoMappingTable';
import AssessmentForm from './AssessmentForm';

import api from '../../../utils/api';

const CoPoAssessmentDashboard = ({ facultyId, classData }) => {
    const [activeTab, setActiveTab] = useState('overview');
    const [selectedSubject, setSelectedSubject] = useState('');
    const [summaryData, setSummaryData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    // Get available subjects from classData
    const availableSubjects = classData?.subjects || [];

    useEffect(() => {
        if (classData) {
            fetchSummaryData();
        }
    }, [classData]);

    const fetchSummaryData = async () => {
        try {
            setLoading(true);
            setError(null);

            const params = {
                academicYear: classData.academicYear,
                semester: classData.semester,
                branch: classData.branch,
                section: classData.section
            };

            const response = await api.get(`/api/copo-assessment/summary/${facultyId}`, { params });
            setSummaryData(response.data.data);
        } catch (err) {
            if (err.response?.status === 404) {
                setSummaryData(null);
            } else {
                setError('Failed to fetch summary data');
            }
            console.error('Error fetching summary:', err);
        } finally {
            setLoading(false);
        }
    };

    const renderOverview = () => {
        if (loading) {
            return (
                <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-2 text-gray-600">Loading overview...</p>
                </div>
            );
        }

        if (!summaryData) {
            return (
                <div className="text-center py-8">
                    <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-700 mb-2">No CO-PO Assessments Found</h3>
                    <p className="text-gray-600 mb-4">
                        Start by calculating CO-PO assessments for your subjects
                    </p>
                    <Button onClick={() => setActiveTab('calculation')}>
                        Start Assessment
                    </Button>
                </div>
            );
        }

        return (
            <div className="space-y-6">
                {/* Overall Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card className="p-4 text-center">
                        <div className="text-2xl font-bold text-blue-600">
                            {summaryData.totalSubjects}
                        </div>
                        <div className="text-sm text-gray-600">Total Subjects</div>
                    </Card>
                    <Card className="p-4 text-center">
                        <div className="text-2xl font-bold text-green-600">
                            {summaryData.overallStatistics.totalCOs}
                        </div>
                        <div className="text-sm text-gray-600">Total COs</div>
                    </Card>
                    <Card className="p-4 text-center">
                        <div className="text-2xl font-bold text-purple-600">
                            {summaryData.overallStatistics.totalPOs}
                        </div>
                        <div className="text-sm text-gray-600">Total POs</div>
                    </Card>
                    <Card className="p-4 text-center">
                        <div className="text-2xl font-bold text-orange-600">
                            {summaryData.overallStatistics.coAttainmentPercentage.toFixed(1)}%
                        </div>
                        <div className="text-sm text-gray-600">CO Attainment</div>
                    </Card>
                </div>

                {/* Subject-wise Summary */}
                <Card>
                    <CardHeader>
                        <CardTitle>Subject-wise Assessment Summary</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Subject
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            CO Attainment
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            PO Attainment
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Last Updated
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {summaryData.subjectWiseSummary.map((subject, index) => (
                                        <tr key={index} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">
                                                        {subject.subjectCode}
                                                    </div>
                                                    <div className="text-sm text-gray-500">
                                                        {subject.subjectName}
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900">
                                                    {subject.coAttainmentPercentage.toFixed(1)}%
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900">
                                                    {subject.poAttainmentPercentage.toFixed(1)}%
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <Badge variant={subject.isFinalized ? 'success' : 'secondary'}>
                                                    {subject.isFinalized ? 'Finalized' : 'Draft'}
                                                </Badge>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {new Date(subject.lastCalculated).toLocaleDateString()}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <Button
                                                    size="sm"
                                                    variant="outline"
                                                    onClick={() => {
                                                        setSelectedSubject(subject.subjectCode);
                                                        setActiveTab('calculation');
                                                    }}
                                                >
                                                    View Details
                                                </Button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </CardContent>
                </Card>

                {/* Quick Actions */}
                <Card>
                    <CardHeader>
                        <CardTitle>Quick Actions</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <Button
                                className="flex items-center gap-2 h-auto p-4"
                                onClick={() => setActiveTab('calculation')}
                            >
                                <Calculator className="h-5 w-5" />
                                <div className="text-left">
                                    <div className="font-medium">Calculate Assessment</div>
                                    <div className="text-sm opacity-80">Compute CO-PO attainments</div>
                                </div>
                            </Button>
                            <Button
                                variant="outline"
                                className="flex items-center gap-2 h-auto p-4"
                                onClick={() => setActiveTab('matrix')}
                            >
                                <Grid className="h-5 w-5" />
                                <div className="text-left">
                                    <div className="font-medium">View Matrix</div>
                                    <div className="text-sm opacity-80">CO-PO correlation matrix</div>
                                </div>
                            </Button>
                            <Button
                                variant="outline"
                                className="flex items-center gap-2 h-auto p-4"
                                onClick={() => setActiveTab('mapping')}
                            >
                                <Target className="h-5 w-5" />
                                <div className="text-left">
                                    <div className="font-medium">CO-PO Mapping</div>
                                    <div className="text-sm opacity-80">Configure mappings</div>
                                </div>
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        );
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-6 w-6" />
                        CO-PO Assessment Dashboard
                    </CardTitle>
                    {classData && (
                        <div className="text-sm text-gray-600">
                            {classData.branch} - {classData.section} | 
                            Academic Year: {classData.academicYear} | 
                            Semester: {classData.semester}
                        </div>
                    )}
                </CardHeader>
            </Card>

            {/* Error Display */}
            {error && (
                <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {/* Main Content */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-5">
                    <TabsTrigger value="overview" className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        Overview
                    </TabsTrigger>
                    <TabsTrigger value="calculation" className="flex items-center gap-2">
                        <Calculator className="h-4 w-4" />
                        Calculate
                    </TabsTrigger>
                    <TabsTrigger value="matrix" className="flex items-center gap-2">
                        <Grid className="h-4 w-4" />
                        Matrix
                    </TabsTrigger>
                    <TabsTrigger value="mapping" className="flex items-center gap-2">
                        <Target className="h-4 w-4" />
                        Mapping
                    </TabsTrigger>
                    <TabsTrigger value="assessment" className="flex items-center gap-2">
                        <BookOpen className="h-4 w-4" />
                        Assessment
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-4">
                    {renderOverview()}
                </TabsContent>

                <TabsContent value="calculation" className="space-y-4">
                    <CoPoCalculation 
                        facultyId={facultyId} 
                        classData={classData}
                    />
                </TabsContent>

                <TabsContent value="matrix" className="space-y-4">
                    <div className="mb-4">
                        <label className="block text-sm font-medium mb-2">
                            Select Subject for Matrix View
                        </label>
                        <select
                            value={selectedSubject}
                            onChange={(e) => setSelectedSubject(e.target.value)}
                            className="w-full max-w-md p-2 border border-gray-300 rounded-md"
                        >
                            <option value="">Select a subject...</option>
                            {availableSubjects.map((subject) => (
                                <option key={subject.code} value={subject.code}>
                                    {subject.code} - {subject.name}
                                </option>
                            ))}
                        </select>
                    </div>
                    <CoPoCorrelationMatrix 
                        facultyId={facultyId} 
                        classData={classData}
                        selectedSubject={selectedSubject}
                    />
                </TabsContent>

                <TabsContent value="mapping" className="space-y-4">
                    <CoPoMappingTable 
                        facultyId={facultyId} 
                        classData={classData}
                    />
                </TabsContent>

                <TabsContent value="assessment" className="space-y-4">
                    <AssessmentForm 
                        facultyId={facultyId} 
                        classData={classData}
                    />
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default CoPoAssessmentDashboard;
