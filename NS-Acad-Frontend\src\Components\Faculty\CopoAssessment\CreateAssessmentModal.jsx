import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "../../ui/card";
import { Button } from "../../ui/button";
import { Alert, AlertDescription } from "../../ui/alert";
import { X, Plus, Save } from 'lucide-react';

const CreateAssessmentModal = ({ isOpen, onClose, facultyId, classData, onAssessmentCreated }) => {
    const [formData, setFormData] = useState({
        type: 'tms',
        tmsType: 'Tutorial',
        assessmentNumber: 1,
        subjectCode: '',
        numberOfStudents: 30
    });
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError(null);

        try {
            // Find the selected subject
            const selectedSubject = classData.subjects.find(s => s.code === formData.subjectCode);
            if (!selectedSubject) {
                throw new Error('Please select a valid subject');
            }

            // Prepare assessment data
            const assessmentData = {
                facultyId: facultyId,
                type: formData.type,
                subject: {
                    code: selectedSubject.code,
                    name: selectedSubject.name
                },
                academicYear: classData.academicYear,
                semester: classData.semester,
                branch: classData.branch,
                section: classData.section,
                numberOfStudents: parseInt(formData.numberOfStudents),
                students: [] // Empty initially, will be populated later
            };

            // Add type-specific fields
            if (formData.type === 'tms') {
                assessmentData.tmsType = formData.tmsType;
            } else if (formData.type === 'tca') {
                assessmentData.assessmentNumber = parseInt(formData.assessmentNumber);
            }

            // Create assessment via API
            const response = await fetch('/api/assessments', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify(assessmentData)
            });

            if (!response.ok) {
                throw new Error('Failed to create assessment');
            }

            const result = await response.json();
            
            // Notify parent component
            if (onAssessmentCreated) {
                onAssessmentCreated(result);
            }

            // Close modal and reset form
            onClose();
            setFormData({
                type: 'tms',
                tmsType: 'Tutorial',
                assessmentNumber: 1,
                subjectCode: '',
                numberOfStudents: 30
            });

        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
                <Card className="border-0 shadow-none">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
                        <CardTitle>Create New Assessment</CardTitle>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={onClose}
                            className="h-8 w-8 p-0"
                        >
                            <X className="h-4 w-4" />
                        </Button>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-4">
                            {error && (
                                <Alert variant="destructive">
                                    <AlertDescription>{error}</AlertDescription>
                                </Alert>
                            )}

                            {/* Assessment Type */}
                            <div>
                                <label className="block text-sm font-medium mb-2">
                                    Assessment Type
                                </label>
                                <select
                                    value={formData.type}
                                    onChange={(e) => handleInputChange('type', e.target.value)}
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                    required
                                >
                                    <option value="tms">TMS (Theory Mid Sem)</option>
                                    <option value="tca">TCA (Theory Continuous Assessment)</option>
                                    <option value="tes">TES (Theory End Sem)</option>
                                </select>
                            </div>

                            {/* TMS Type (only for TMS) */}
                            {formData.type === 'tms' && (
                                <div>
                                    <label className="block text-sm font-medium mb-2">
                                        TMS Type
                                    </label>
                                    <select
                                        value={formData.tmsType}
                                        onChange={(e) => handleInputChange('tmsType', e.target.value)}
                                        className="w-full p-2 border border-gray-300 rounded-md"
                                        required
                                    >
                                        <option value="Tutorial">Tutorial</option>
                                        <option value="MiniProject">Mini Project</option>
                                        <option value="SurpriseTest">Surprise Test</option>
                                    </select>
                                </div>
                            )}

                            {/* Assessment Number (only for TCA) */}
                            {formData.type === 'tca' && (
                                <div>
                                    <label className="block text-sm font-medium mb-2">
                                        Assessment Number
                                    </label>
                                    <select
                                        value={formData.assessmentNumber}
                                        onChange={(e) => handleInputChange('assessmentNumber', e.target.value)}
                                        className="w-full p-2 border border-gray-300 rounded-md"
                                        required
                                    >
                                        <option value={1}>TCA 1</option>
                                        <option value={2}>TCA 2</option>
                                    </select>
                                </div>
                            )}

                            {/* Subject */}
                            <div>
                                <label className="block text-sm font-medium mb-2">
                                    Subject
                                </label>
                                <select
                                    value={formData.subjectCode}
                                    onChange={(e) => handleInputChange('subjectCode', e.target.value)}
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                    required
                                >
                                    <option value="">Select Subject</option>
                                    {classData?.subjects?.map((subject) => (
                                        <option key={subject.code} value={subject.code}>
                                            {subject.code} - {subject.name}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            {/* Number of Students */}
                            <div>
                                <label className="block text-sm font-medium mb-2">
                                    Number of Students
                                </label>
                                <input
                                    type="number"
                                    value={formData.numberOfStudents}
                                    onChange={(e) => handleInputChange('numberOfStudents', e.target.value)}
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                    min="1"
                                    max="200"
                                    required
                                />
                            </div>

                            {/* Class Information (Read-only) */}
                            <div className="bg-gray-50 p-3 rounded-md">
                                <h4 className="text-sm font-medium mb-2">Class Information</h4>
                                <div className="text-sm text-gray-600 space-y-1">
                                    <div>Branch: {classData?.branch}</div>
                                    <div>Section: {classData?.section}</div>
                                    <div>Academic Year: {classData?.academicYear}</div>
                                    <div>Semester: {classData?.semester}</div>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex justify-end gap-2 pt-4">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={onClose}
                                    disabled={loading}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="submit"
                                    disabled={loading}
                                    className="flex items-center gap-2"
                                >
                                    {loading ? (
                                        <>
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                            Creating...
                                        </>
                                    ) : (
                                        <>
                                            <Save className="h-4 w-4" />
                                            Create Assessment
                                        </>
                                    )}
                                </Button>
                            </div>
                        </form>

                        <div className="mt-4 p-3 bg-blue-50 rounded-md">
                            <p className="text-sm text-blue-800">
                                <strong>Note:</strong> This will create an empty assessment structure. 
                                You can add student data and marks after creation.
                            </p>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};

export default CreateAssessmentModal;
