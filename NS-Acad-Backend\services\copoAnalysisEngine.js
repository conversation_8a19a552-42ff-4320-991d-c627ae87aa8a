const CoPoAssessment = require('../models/CoPoAssessment');
const CoPoMapping = require('../models/CoPoMapping');

class CoPoAnalysisEngine {
    constructor() {
        this.analysisConfig = {
            attainmentLevels: {
                0: { label: 'Not Attained', color: '#ef4444', threshold: 0 },
                1: { label: 'Partially Attained', color: '#f59e0b', threshold: 40 },
                2: { label: 'Substantially Attained', color: '#10b981', threshold: 60 },
                3: { label: 'Fully Attained', color: '#059669', threshold: 80 }
            },
            reportTypes: ['detailed', 'summary', 'matrix', 'trends']
        };
    }

    /**
     * Generate comprehensive CO-PO analysis report
     */
    async generateAnalysisReport(facultyId, filters = {}) {
        try {
            const query = { facultyId };
            if (filters.academicYear) query.academicYear = filters.academicYear;
            if (filters.semester) query.semester = filters.semester;
            if (filters.branch) query.branch = filters.branch;
            if (filters.section) query.section = filters.section;
            if (filters.subjectCode) query.subjectCode = filters.subjectCode;

            const assessments = await CoPoAssessment.find(query).lean();

            if (assessments.length === 0) {
                throw new Error('No CO-PO assessments found for the specified criteria');
            }

            const report = {
                metadata: {
                    generatedAt: new Date(),
                    facultyId: facultyId,
                    filters: filters,
                    totalSubjects: assessments.length
                },
                executiveSummary: this.generateExecutiveSummary(assessments),
                coAnalysis: this.generateCoAnalysis(assessments),
                poAnalysis: this.generatePoAnalysis(assessments),
                correlationAnalysis: this.generateCorrelationAnalysis(assessments),
                trendAnalysis: this.generateTrendAnalysis(assessments),
                recommendations: this.generateRecommendations(assessments)
            };

            return report;

        } catch (error) {
            console.error('Error generating analysis report:', error);
            throw error;
        }
    }

    /**
     * Generate executive summary
     */
    generateExecutiveSummary(assessments) {
        const totalCOs = assessments.reduce((sum, a) => sum + a.overallStatistics.totalCOs, 0);
        const attainedCOs = assessments.reduce((sum, a) => sum + a.overallStatistics.attainedCOs, 0);
        const totalPOs = assessments.reduce((sum, a) => sum + a.overallStatistics.totalPOs, 0);
        const attainedPOs = assessments.reduce((sum, a) => sum + a.overallStatistics.attainedPOs, 0);

        const coAttainmentRate = totalCOs > 0 ? (attainedCOs / totalCOs) * 100 : 0;
        const poAttainmentRate = totalPOs > 0 ? (attainedPOs / totalPOs) * 100 : 0;

        // Calculate attainment distribution
        const coAttainmentDistribution = { 0: 0, 1: 0, 2: 0, 3: 0 };
        const poAttainmentDistribution = { 0: 0, 1: 0, 2: 0, 3: 0 };

        assessments.forEach(assessment => {
            assessment.coAttainments.forEach(co => {
                coAttainmentDistribution[co.overallAttainment.attainmentLevel]++;
            });
            assessment.poAttainments.forEach(po => {
                poAttainmentDistribution[po.attainmentLevel]++;
            });
        });

        return {
            overallPerformance: {
                coAttainmentRate: Math.round(coAttainmentRate * 100) / 100,
                poAttainmentRate: Math.round(poAttainmentRate * 100) / 100,
                totalSubjectsAnalyzed: assessments.length,
                performanceGrade: this.calculatePerformanceGrade(coAttainmentRate, poAttainmentRate)
            },
            attainmentDistribution: {
                courseOutcomes: coAttainmentDistribution,
                programOutcomes: poAttainmentDistribution
            },
            keyInsights: this.generateKeyInsights(assessments, coAttainmentRate, poAttainmentRate)
        };
    }

    /**
     * Generate CO-specific analysis
     */
    generateCoAnalysis(assessments) {
        const coData = {};

        // Aggregate CO data across all assessments
        assessments.forEach(assessment => {
            assessment.coAttainments.forEach(co => {
                if (!coData[co.coIdentifier]) {
                    coData[co.coIdentifier] = {
                        coIdentifier: co.coIdentifier,
                        occurrences: 0,
                        totalAttainment: 0,
                        attainmentLevels: [],
                        subjects: []
                    };
                }
                coData[co.coIdentifier].occurrences++;
                coData[co.coIdentifier].totalAttainment += co.overallAttainment.weightedPercentage;
                coData[co.coIdentifier].attainmentLevels.push(co.overallAttainment.attainmentLevel);
                coData[co.coIdentifier].subjects.push({
                    subjectCode: assessment.subjectCode,
                    subjectName: assessment.subjectName,
                    attainment: co.overallAttainment.weightedPercentage,
                    level: co.overallAttainment.attainmentLevel
                });
            });
        });

        // Calculate statistics for each CO
        const coAnalysis = Object.values(coData).map(co => {
            const averageAttainment = co.totalAttainment / co.occurrences;
            const levelDistribution = { 0: 0, 1: 0, 2: 0, 3: 0 };
            co.attainmentLevels.forEach(level => levelDistribution[level]++);

            return {
                coIdentifier: co.coIdentifier,
                statistics: {
                    averageAttainment: Math.round(averageAttainment * 100) / 100,
                    occurrences: co.occurrences,
                    attainmentRate: (co.attainmentLevels.filter(l => l >= 2).length / co.occurrences) * 100,
                    levelDistribution: levelDistribution
                },
                subjects: co.subjects,
                performance: this.evaluateCoPerformance(averageAttainment, levelDistribution)
            };
        });

        return {
            totalUniqueCOs: coAnalysis.length,
            coDetails: coAnalysis.sort((a, b) => a.coIdentifier.localeCompare(b.coIdentifier)),
            topPerformingCOs: coAnalysis
                .sort((a, b) => b.statistics.averageAttainment - a.statistics.averageAttainment)
                .slice(0, 5),
            underperformingCOs: coAnalysis
                .filter(co => co.statistics.averageAttainment < 60)
                .sort((a, b) => a.statistics.averageAttainment - b.statistics.averageAttainment)
        };
    }

    /**
     * Generate PO-specific analysis
     */
    generatePoAnalysis(assessments) {
        const poData = {};

        // Aggregate PO data across all assessments
        assessments.forEach(assessment => {
            assessment.poAttainments.forEach(po => {
                if (!poData[po.poIdentifier]) {
                    poData[po.poIdentifier] = {
                        poIdentifier: po.poIdentifier,
                        occurrences: 0,
                        totalAttainment: 0,
                        attainmentLevels: [],
                        mappedCOs: new Set(),
                        subjects: []
                    };
                }
                poData[po.poIdentifier].occurrences++;
                poData[po.poIdentifier].totalAttainment += po.attainmentPercentage;
                poData[po.poIdentifier].attainmentLevels.push(po.attainmentLevel);
                po.mappedCOs.forEach(co => poData[po.poIdentifier].mappedCOs.add(co.coIdentifier));
                poData[po.poIdentifier].subjects.push({
                    subjectCode: assessment.subjectCode,
                    subjectName: assessment.subjectName,
                    attainment: po.attainmentPercentage,
                    level: po.attainmentLevel
                });
            });
        });

        // Calculate statistics for each PO
        const poAnalysis = Object.values(poData).map(po => {
            const averageAttainment = po.totalAttainment / po.occurrences;
            const levelDistribution = { 0: 0, 1: 0, 2: 0, 3: 0 };
            po.attainmentLevels.forEach(level => levelDistribution[level]++);

            return {
                poIdentifier: po.poIdentifier,
                statistics: {
                    averageAttainment: Math.round(averageAttainment * 100) / 100,
                    occurrences: po.occurrences,
                    attainmentRate: (po.attainmentLevels.filter(l => l >= 2).length / po.occurrences) * 100,
                    levelDistribution: levelDistribution,
                    uniqueMappedCOs: po.mappedCOs.size
                },
                subjects: po.subjects,
                mappedCOs: Array.from(po.mappedCOs),
                performance: this.evaluatePoPerformance(averageAttainment, levelDistribution)
            };
        });

        return {
            totalUniquePOs: poAnalysis.length,
            poDetails: poAnalysis.sort((a, b) => a.poIdentifier.localeCompare(b.poIdentifier)),
            topPerformingPOs: poAnalysis
                .sort((a, b) => b.statistics.averageAttainment - a.statistics.averageAttainment)
                .slice(0, 5),
            underperformingPOs: poAnalysis
                .filter(po => po.statistics.averageAttainment < 60)
                .sort((a, b) => a.statistics.averageAttainment - b.statistics.averageAttainment)
        };
    }

    /**
     * Generate correlation analysis
     */
    generateCorrelationAnalysis(assessments) {
        const correlations = [];

        assessments.forEach(assessment => {
            const subjectCorrelation = {
                subjectCode: assessment.subjectCode,
                subjectName: assessment.subjectName,
                coPoCorrelations: []
            };

            assessment.coAttainments.forEach(co => {
                assessment.poAttainments.forEach(po => {
                    const mappedCO = po.mappedCOs.find(mco => mco.coIdentifier === co.coIdentifier);
                    if (mappedCO) {
                        subjectCorrelation.coPoCorrelations.push({
                            coIdentifier: co.coIdentifier,
                            poIdentifier: po.poIdentifier,
                            mappingStrength: mappedCO.mappingStrength,
                            coAttainment: co.overallAttainment.attainmentLevel,
                            poAttainment: po.attainmentLevel,
                            correlation: mappedCO.weightedContribution,
                            effectiveness: this.calculateCorrelationEffectiveness(
                                mappedCO.mappingStrength, 
                                co.overallAttainment.attainmentLevel, 
                                po.attainmentLevel
                            )
                        });
                    }
                });
            });

            correlations.push(subjectCorrelation);
        });

        return {
            subjectWiseCorrelations: correlations,
            overallCorrelationStrength: this.calculateOverallCorrelationStrength(correlations),
            strongestCorrelations: this.findStrongestCorrelations(correlations),
            weakestCorrelations: this.findWeakestCorrelations(correlations)
        };
    }

    /**
     * Generate trend analysis
     */
    generateTrendAnalysis(assessments) {
        // Group assessments by academic year for trend analysis
        const yearlyData = {};
        
        assessments.forEach(assessment => {
            const year = assessment.academicYear;
            if (!yearlyData[year]) {
                yearlyData[year] = {
                    year: year,
                    subjects: 0,
                    totalCOs: 0,
                    attainedCOs: 0,
                    totalPOs: 0,
                    attainedPOs: 0
                };
            }
            
            yearlyData[year].subjects++;
            yearlyData[year].totalCOs += assessment.overallStatistics.totalCOs;
            yearlyData[year].attainedCOs += assessment.overallStatistics.attainedCOs;
            yearlyData[year].totalPOs += assessment.overallStatistics.totalPOs;
            yearlyData[year].attainedPOs += assessment.overallStatistics.attainedPOs;
        });

        const trends = Object.values(yearlyData).map(data => ({
            ...data,
            coAttainmentRate: data.totalCOs > 0 ? (data.attainedCOs / data.totalCOs) * 100 : 0,
            poAttainmentRate: data.totalPOs > 0 ? (data.attainedPOs / data.totalPOs) * 100 : 0
        })).sort((a, b) => a.year.localeCompare(b.year));

        return {
            yearlyTrends: trends,
            trendDirection: this.calculateTrendDirection(trends),
            improvementAreas: this.identifyImprovementAreas(trends)
        };
    }

    /**
     * Generate recommendations based on analysis
     */
    generateRecommendations(assessments) {
        const recommendations = [];

        // Analyze overall performance
        const totalCOs = assessments.reduce((sum, a) => sum + a.overallStatistics.totalCOs, 0);
        const attainedCOs = assessments.reduce((sum, a) => sum + a.overallStatistics.attainedCOs, 0);
        const coAttainmentRate = totalCOs > 0 ? (attainedCOs / totalCOs) * 100 : 0;

        if (coAttainmentRate < 60) {
            recommendations.push({
                type: 'critical',
                category: 'CO Attainment',
                title: 'Improve Course Outcome Attainment',
                description: 'Overall CO attainment is below the 60% threshold. Consider revising teaching methodologies and assessment strategies.',
                priority: 'high',
                actionItems: [
                    'Review and update course delivery methods',
                    'Implement additional assessment tools',
                    'Provide supplementary learning resources',
                    'Conduct regular student feedback sessions'
                ]
            });
        }

        // Add more specific recommendations based on analysis
        // ... (additional recommendation logic)

        return recommendations;
    }

    // Helper methods
    calculatePerformanceGrade(coRate, poRate) {
        const average = (coRate + poRate) / 2;
        if (average >= 80) return 'A';
        if (average >= 70) return 'B';
        if (average >= 60) return 'C';
        if (average >= 50) return 'D';
        return 'F';
    }

    generateKeyInsights(assessments, coRate, poRate) {
        const insights = [];
        
        if (coRate > 80) {
            insights.push('Excellent CO attainment across subjects');
        } else if (coRate < 50) {
            insights.push('CO attainment needs significant improvement');
        }

        if (poRate > 80) {
            insights.push('Strong PO attainment indicating effective curriculum mapping');
        }

        return insights;
    }

    evaluateCoPerformance(averageAttainment, levelDistribution) {
        if (averageAttainment >= 80) return 'Excellent';
        if (averageAttainment >= 60) return 'Good';
        if (averageAttainment >= 40) return 'Satisfactory';
        return 'Needs Improvement';
    }

    evaluatePoPerformance(averageAttainment, levelDistribution) {
        return this.evaluateCoPerformance(averageAttainment, levelDistribution);
    }

    calculateCorrelationEffectiveness(mappingStrength, coLevel, poLevel) {
        return (mappingStrength * Math.min(coLevel, poLevel)) / (mappingStrength * 3);
    }

    calculateOverallCorrelationStrength(correlations) {
        let totalCorrelations = 0;
        let strongCorrelations = 0;

        correlations.forEach(subject => {
            subject.coPoCorrelations.forEach(corr => {
                totalCorrelations++;
                if (corr.effectiveness > 0.7) strongCorrelations++;
            });
        });

        return totalCorrelations > 0 ? (strongCorrelations / totalCorrelations) * 100 : 0;
    }

    findStrongestCorrelations(correlations) {
        const allCorrelations = [];
        correlations.forEach(subject => {
            subject.coPoCorrelations.forEach(corr => {
                allCorrelations.push({ ...corr, subjectCode: subject.subjectCode });
            });
        });

        return allCorrelations
            .sort((a, b) => b.effectiveness - a.effectiveness)
            .slice(0, 10);
    }

    findWeakestCorrelations(correlations) {
        const allCorrelations = [];
        correlations.forEach(subject => {
            subject.coPoCorrelations.forEach(corr => {
                allCorrelations.push({ ...corr, subjectCode: subject.subjectCode });
            });
        });

        return allCorrelations
            .filter(corr => corr.effectiveness < 0.3)
            .sort((a, b) => a.effectiveness - b.effectiveness)
            .slice(0, 10);
    }

    calculateTrendDirection(trends) {
        if (trends.length < 2) return 'insufficient_data';
        
        const latest = trends[trends.length - 1];
        const previous = trends[trends.length - 2];
        
        const coTrend = latest.coAttainmentRate - previous.coAttainmentRate;
        const poTrend = latest.poAttainmentRate - previous.poAttainmentRate;
        
        if (coTrend > 5 && poTrend > 5) return 'improving';
        if (coTrend < -5 && poTrend < -5) return 'declining';
        return 'stable';
    }

    identifyImprovementAreas(trends) {
        const areas = [];
        
        const latestTrend = trends[trends.length - 1];
        if (latestTrend.coAttainmentRate < 60) {
            areas.push('Course Outcome Attainment');
        }
        if (latestTrend.poAttainmentRate < 60) {
            areas.push('Program Outcome Attainment');
        }
        
        return areas;
    }
}

module.exports = new CoPoAnalysisEngine();
