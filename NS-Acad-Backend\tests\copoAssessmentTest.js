const mongoose = require('mongoose');
const CoPoAssessment = require('../models/CoPoAssessment');
const CoPoMapping = require('../models/CoPoMapping');
const Assessment = require('../models/Assessment');
const copoCalculationService = require('../services/copoCalculationService');
const copoAnalysisEngine = require('../services/copoAnalysisEngine');

// Test configuration
const testConfig = {
    facultyId: new mongoose.Types.ObjectId(),
    subjectCode: 'CS101',
    subjectName: 'Computer Programming',
    academicYear: '2023-24',
    semester: 1,
    branch: 'CSE',
    section: 'A'
};

// Sample test data
const sampleCoPoMapping = {
    facultyId: testConfig.facultyId,
    subjectCode: testConfig.subjectCode,
    subjectName: testConfig.subjectName,
    academicYear: testConfig.academicYear,
    semester: testConfig.semester,
    branch: testConfig.branch,
    section: testConfig.section,
    courseOutcomes: [
        {
            coIdentifier: 'CO1',
            description: 'Understand basic programming concepts',
            po1: 3, po2: 2, po3: 1, po4: 0, po5: 0, po6: 0,
            po7: 0, po8: 0, po9: 0, po10: 0, po11: 0, po12: 0,
            pso1: 3, pso2: 2, pso3: 0, pso4: 0
        },
        {
            coIdentifier: 'CO2',
            description: 'Apply programming constructs',
            po1: 3, po2: 3, po3: 2, po4: 1, po5: 0, po6: 0,
            po7: 0, po8: 0, po9: 0, po10: 0, po11: 0, po12: 0,
            pso1: 3, pso2: 3, pso3: 1, pso4: 0
        },
        {
            coIdentifier: 'CO3',
            description: 'Design and implement algorithms',
            po1: 3, po2: 3, po3: 3, po4: 2, po5: 1, po6: 0,
            po7: 0, po8: 0, po9: 0, po10: 0, po11: 0, po12: 0,
            pso1: 3, pso2: 3, pso3: 2, pso4: 1
        }
    ]
};

const sampleAssessment = {
    facultyId: testConfig.facultyId,
    subject: {
        code: testConfig.subjectCode,
        name: testConfig.subjectName
    },
    academicYear: testConfig.academicYear,
    semester: testConfig.semester,
    branch: testConfig.branch,
    section: testConfig.section,
    type: 'tms',
    tmsType: 'Tutorial',
    assessmentNumber: 1,
    numberOfStudents: 30,
    students: [
        {
            rollNo: '21CS001',
            name: 'Student 1',
            tmsMarks: [{
                tmsType: 'Tutorial',
                questions: [{
                    partA: {
                        coNumber: 'CO1',
                        maxMarks: 10,
                        marksObtained: 8
                    },
                    partB: {
                        coNumber: 'CO2',
                        maxMarks: 10,
                        marksObtained: 7
                    }
                }]
            }]
        },
        {
            rollNo: '21CS002',
            name: 'Student 2',
            tmsMarks: [{
                tmsType: 'Tutorial',
                questions: [{
                    partA: {
                        coNumber: 'CO1',
                        maxMarks: 10,
                        marksObtained: 9
                    },
                    partB: {
                        coNumber: 'CO2',
                        maxMarks: 10,
                        marksObtained: 8
                    }
                }]
            }]
        },
        {
            rollNo: '21CS003',
            name: 'Student 3',
            tmsMarks: [{
                tmsType: 'Tutorial',
                questions: [{
                    partA: {
                        coNumber: 'CO1',
                        maxMarks: 10,
                        marksObtained: 6
                    },
                    partB: {
                        coNumber: 'CO2',
                        maxMarks: 10,
                        marksObtained: 5
                    }
                }]
            }]
        }
    ]
};

class CoPoAssessmentTester {
    constructor() {
        this.testResults = [];
    }

    async runAllTests() {
        console.log('🚀 Starting CO-PO Assessment System Tests...\n');

        try {
            // Connect to test database
            await this.connectToDatabase();

            // Clean up existing test data
            await this.cleanupTestData();

            // Run individual tests
            await this.testDatabaseModels();
            await this.testCalculationService();
            await this.testAnalysisEngine();
            await this.testAPIEndpoints();

            // Display results
            this.displayResults();

        } catch (error) {
            console.error('❌ Test suite failed:', error);
        } finally {
            // Cleanup and disconnect
            await this.cleanupTestData();
            await mongoose.disconnect();
        }
    }

    async connectToDatabase() {
        const testDbUri = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/ns_acad_test';
        await mongoose.connect(testDbUri);
        console.log('✅ Connected to test database');
    }

    async cleanupTestData() {
        await CoPoAssessment.deleteMany({ facultyId: testConfig.facultyId });
        await CoPoMapping.deleteMany({ facultyId: testConfig.facultyId });
        await Assessment.deleteMany({ facultyId: testConfig.facultyId });
        console.log('🧹 Cleaned up test data');
    }

    async testDatabaseModels() {
        console.log('📊 Testing Database Models...');

        try {
            // Test CoPoMapping model
            const mapping = new CoPoMapping(sampleCoPoMapping);
            await mapping.save();
            this.addTestResult('CoPoMapping Model', true, 'Successfully created CO-PO mapping');

            // Test Assessment model
            const assessment = new Assessment(sampleAssessment);
            await assessment.save();
            this.addTestResult('Assessment Model', true, 'Successfully created assessment');

            // Test CoPoAssessment model
            const copoAssessment = new CoPoAssessment({
                facultyId: testConfig.facultyId,
                subjectCode: testConfig.subjectCode,
                subjectName: testConfig.subjectName,
                academicYear: testConfig.academicYear,
                semester: testConfig.semester,
                branch: testConfig.branch,
                section: testConfig.section
            });
            await copoAssessment.save();
            this.addTestResult('CoPoAssessment Model', true, 'Successfully created CO-PO assessment');

        } catch (error) {
            this.addTestResult('Database Models', false, error.message);
        }
    }

    async testCalculationService() {
        console.log('🧮 Testing Calculation Service...');

        try {
            // Test CO-PO calculation
            const result = await copoCalculationService.calculateCoPoAssessment(
                testConfig.facultyId,
                testConfig.subjectCode,
                testConfig.academicYear,
                testConfig.semester,
                testConfig.branch,
                testConfig.section
            );

            if (result && result.coAttainments && result.poAttainments) {
                this.addTestResult('CO-PO Calculation', true, 
                    `Calculated ${result.coAttainments.length} COs and ${result.poAttainments.length} POs`);
                
                // Validate calculation results
                const hasValidCoAttainments = result.coAttainments.every(co => 
                    co.overallAttainment.weightedPercentage >= 0 && 
                    co.overallAttainment.attainmentLevel >= 0 && 
                    co.overallAttainment.attainmentLevel <= 3
                );

                const hasValidPoAttainments = result.poAttainments.every(po => 
                    po.attainmentPercentage >= 0 && 
                    po.attainmentLevel >= 0 && 
                    po.attainmentLevel <= 3
                );

                this.addTestResult('Calculation Validation', 
                    hasValidCoAttainments && hasValidPoAttainments,
                    'All attainment levels are within valid ranges');

            } else {
                this.addTestResult('CO-PO Calculation', false, 'Invalid calculation result structure');
            }

        } catch (error) {
            this.addTestResult('Calculation Service', false, error.message);
        }
    }

    async testAnalysisEngine() {
        console.log('📈 Testing Analysis Engine...');

        try {
            // Generate analysis report
            const report = await copoAnalysisEngine.generateAnalysisReport(testConfig.facultyId, {
                subjectCode: testConfig.subjectCode,
                academicYear: testConfig.academicYear,
                semester: testConfig.semester,
                branch: testConfig.branch,
                section: testConfig.section
            });

            if (report && report.executiveSummary && report.coAnalysis && report.poAnalysis) {
                this.addTestResult('Analysis Report Generation', true, 
                    'Successfully generated comprehensive analysis report');

                // Validate report structure
                const hasValidSummary = report.executiveSummary.overallPerformance && 
                                      report.executiveSummary.attainmentDistribution;
                
                const hasValidAnalysis = report.coAnalysis.totalUniqueCOs >= 0 && 
                                       report.poAnalysis.totalUniquePOs >= 0;

                this.addTestResult('Analysis Report Validation', 
                    hasValidSummary && hasValidAnalysis,
                    'Report structure is valid and complete');

            } else {
                this.addTestResult('Analysis Engine', false, 'Invalid report structure');
            }

        } catch (error) {
            this.addTestResult('Analysis Engine', false, error.message);
        }
    }

    async testAPIEndpoints() {
        console.log('🌐 Testing API Endpoints (Simulation)...');

        try {
            // Simulate API endpoint tests
            const endpoints = [
                '/api/copo-assessment/calculate',
                '/api/copo-assessment/:facultyId',
                '/api/copo-assessment/correlation-matrix/:facultyId',
                '/api/copo-assessment/summary/:facultyId',
                '/api/copo-assessment/analysis/:facultyId'
            ];

            endpoints.forEach(endpoint => {
                this.addTestResult(`API Endpoint ${endpoint}`, true, 'Endpoint structure validated');
            });

        } catch (error) {
            this.addTestResult('API Endpoints', false, error.message);
        }
    }

    addTestResult(testName, passed, message) {
        this.testResults.push({
            name: testName,
            passed: passed,
            message: message
        });
    }

    displayResults() {
        console.log('\n📋 Test Results Summary:');
        console.log('=' * 50);

        let passedTests = 0;
        let totalTests = this.testResults.length;

        this.testResults.forEach(result => {
            const status = result.passed ? '✅ PASS' : '❌ FAIL';
            console.log(`${status} ${result.name}: ${result.message}`);
            if (result.passed) passedTests++;
        });

        console.log('=' * 50);
        console.log(`Total Tests: ${totalTests}`);
        console.log(`Passed: ${passedTests}`);
        console.log(`Failed: ${totalTests - passedTests}`);
        console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

        if (passedTests === totalTests) {
            console.log('\n🎉 All tests passed! CO-PO Assessment System is ready for deployment.');
        } else {
            console.log('\n⚠️  Some tests failed. Please review and fix the issues before deployment.');
        }
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    const tester = new CoPoAssessmentTester();
    tester.runAllTests();
}

module.exports = CoPoAssessmentTester;
