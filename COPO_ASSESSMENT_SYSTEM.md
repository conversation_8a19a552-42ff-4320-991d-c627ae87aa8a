# CO-PO Assessment System

## Overview

The CO-PO (Course Outcome - Program Outcome) Assessment System is a comprehensive solution for academic institutions to evaluate and analyze the attainment of course outcomes and their correlation with program outcomes. This system implements standard methodologies used in engineering education accreditation (ABET, NBA, etc.).

## Features

### 🎯 Core Functionality
- **Automated CO-PO Calculation**: Implements standard formulas with 80% direct assessment and 20% indirect assessment weighting
- **Real-time Assessment Processing**: Processes existing assessment data to calculate attainment levels
- **Correlation Matrix Generation**: Creates comprehensive CO-PO correlation matrices with visual indicators
- **Multi-level Analysis**: Provides detailed analysis at CO, PO, and overall program levels
- **Comprehensive Reporting**: Generates executive summaries, trend analysis, and recommendations

### 📊 Assessment Methodologies
- **Direct Assessment**: Based on actual student performance in assessments (TMS, TCA, TES)
- **Indirect Assessment**: Incorporates feedback and survey data
- **Threshold-based Evaluation**: Configurable thresholds for attainment levels (default: 60%)
- **Weighted Calculation**: Standard 80-20 weighting between direct and indirect assessments

### 🔧 Technical Features
- **RESTful API**: Complete backend API with CRUD operations
- **Real-time Dashboard**: Interactive frontend with multiple views and analysis tools
- **Data Validation**: Comprehensive input validation and error handling
- **Export Capabilities**: CSV export for correlation matrices and reports
- **Responsive Design**: Mobile-friendly interface with modern UI components

## System Architecture

### Backend Components

#### 1. Database Models
- **CoPoAssessment**: Main model storing calculated assessments
- **CoPoMapping**: Stores CO-PO mapping configurations
- **Assessment**: Existing assessment data integration

#### 2. Services
- **copoCalculationService**: Core calculation engine
- **copoAnalysisEngine**: Advanced analysis and reporting

#### 3. API Endpoints
```
POST   /api/copo-assessment/calculate
GET    /api/copo-assessment/:facultyId
GET    /api/copo-assessment/correlation-matrix/:facultyId
GET    /api/copo-assessment/summary/:facultyId
GET    /api/copo-assessment/analysis/:facultyId
PUT    /api/copo-assessment/finalize/:id
DELETE /api/copo-assessment/:id
```

### Frontend Components

#### 1. Dashboard Components
- **CoPoAssessmentDashboard**: Main dashboard with tabbed interface
- **CoPoCalculation**: Calculation interface and results display
- **CoPoCorrelationMatrix**: Interactive correlation matrix viewer
- **CoPoMappingTable**: CO-PO mapping configuration interface

#### 2. Integration
- Seamlessly integrates with existing assessment system
- Maintains current UI/UX design patterns
- Uses existing authentication and authorization

## Calculation Methodology

### CO Attainment Calculation

1. **Direct Assessment Percentage**: 
   ```
   DA% = (Students scoring ≥60% / Total Students) × 100
   ```

2. **Indirect Assessment Percentage**: 
   ```
   IA% = (Feedback scores ≥60% / Total Responses) × 100
   ```

3. **Overall CO Attainment**: 
   ```
   CO Attainment = (DA% × 0.8) + (IA% × 0.2)
   ```

4. **Attainment Levels**:
   - Level 0: < 40% (Not Attained)
   - Level 1: 40-59% (Partially Attained)
   - Level 2: 60-79% (Substantially Attained)
   - Level 3: ≥80% (Fully Attained)

### PO Attainment Calculation

1. **Weighted Sum**: 
   ```
   Weighted Sum = Σ(Mapping Strength × CO Attainment Level)
   ```

2. **Total Mapping Strength**: 
   ```
   Total Strength = Σ(Mapping Strength for all mapped COs)
   ```

3. **PO Attainment Percentage**: 
   ```
   PO% = (Weighted Sum / (Total Strength × 3)) × 100
   ```

## Installation and Setup

### Prerequisites
- Node.js (v14 or higher)
- MongoDB (v4.4 or higher)
- Existing NS-Acad system

### Backend Setup

1. **Install Dependencies**:
   ```bash
   cd NS-Acad-Backend
   npm install
   ```

2. **Environment Variables**:
   ```env
   MONGODB_URI=mongodb://localhost:27017/ns_acad
   FRONTEND_URL=http://localhost:5173
   ```

3. **Start Server**:
   ```bash
   npm start
   ```

### Frontend Setup

1. **Install Dependencies**:
   ```bash
   cd NS-Acad-Frontend
   npm install
   ```

2. **Start Development Server**:
   ```bash
   npm run dev
   ```

## Usage Guide

### 1. Initial Setup
1. Navigate to `/copo` route in the faculty dashboard
2. Ensure CO-PO mappings are configured for your subjects
3. Verify that assessment data is available

### 2. Calculate CO-PO Assessment
1. Select a subject from the dropdown
2. Click "Calculate" to process assessment data
3. Review the calculated CO and PO attainments
4. Generate analysis reports for detailed insights

### 3. View Correlation Matrix
1. Switch to the "Matrix" tab
2. Select a subject to view its correlation matrix
3. Export the matrix as CSV for external analysis
4. Use the legend to interpret correlation strengths

### 4. Analysis and Reporting
1. Use the "Analysis" tab to generate comprehensive reports
2. Review executive summaries and key insights
3. Identify improvement areas and recommendations
4. Track trends across academic years

## API Documentation

### Calculate CO-PO Assessment
```http
POST /api/copo-assessment/calculate
Content-Type: application/json

{
  "facultyId": "faculty_object_id",
  "subjectCode": "CS101",
  "academicYear": "2023-24",
  "semester": 1,
  "branch": "CSE",
  "section": "A"
}
```

### Get Assessment Data
```http
GET /api/copo-assessment/{facultyId}?subjectCode=CS101&academicYear=2023-24&semester=1&branch=CSE&section=A
```

### Generate Correlation Matrix
```http
GET /api/copo-assessment/correlation-matrix/{facultyId}?subjectCode=CS101&academicYear=2023-24&semester=1&branch=CSE&section=A
```

## Testing

### Run Test Suite
```bash
cd NS-Acad-Backend
node tests/copoAssessmentTest.js
```

### Test Coverage
- Database model validation
- Calculation service accuracy
- Analysis engine functionality
- API endpoint structure
- Data integrity checks

## Configuration

### Assessment Configuration
```javascript
{
  directAssessmentWeight: 80,    // 80%
  indirectAssessmentWeight: 20,  // 20%
  attainmentThreshold: 60,       // 60%
  coThresholds: {
    level1: 40,  // 40-59%
    level2: 60,  // 60-79%
    level3: 80   // 80%+
  }
}
```

### Customization Options
- Adjustable weighting percentages
- Configurable threshold levels
- Custom mapping strength scales
- Flexible reporting parameters

## Troubleshooting

### Common Issues

1. **No Assessment Data Found**
   - Ensure assessments are created for the subject
   - Verify faculty assignment to the class
   - Check academic year and semester parameters

2. **CO-PO Mapping Not Found**
   - Create CO-PO mappings using the mapping interface
   - Ensure mappings match the subject and class details
   - Verify mapping strength values (1-3 scale)

3. **Calculation Errors**
   - Check assessment data structure
   - Verify CO identifiers match between assessments and mappings
   - Ensure student data is properly formatted

### Support
For technical support or feature requests, please contact the development team or create an issue in the project repository.

## Future Enhancements

- Integration with external accreditation systems
- Advanced analytics and machine learning insights
- Automated report generation and scheduling
- Multi-language support
- Mobile application development
- Integration with Learning Management Systems (LMS)

## License

This CO-PO Assessment System is part of the NS-Acad project and follows the same licensing terms.
