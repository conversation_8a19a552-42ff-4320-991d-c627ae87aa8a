import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "../../ui/card";
import { But<PERSON> } from "../../ui/button";
import { Alert, AlertDescription } from "../../ui/alert";
import { Badge } from "../../ui/badge";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "../../ui/tabs";
import { Calculator, TrendingUp, FileText, Download } from 'lucide-react';
import api from '../../../utils/api';

const CoPoCalculation = ({ facultyId, classData }) => {
    const [loading, setLoading] = useState(false);
    const [calculationData, setCalculationData] = useState(null);
    const [analysisData, setAnalysisData] = useState(null);
    const [error, setError] = useState(null);
    const [selectedSubject, setSelectedSubject] = useState('');

    // Get available subjects from classData
    const availableSubjects = classData?.subjects || [];

    useEffect(() => {
        if (selectedSubject && classData) {
            fetchCalculationData();
        }
    }, [selectedSubject, classData]);

    const fetchCalculationData = async () => {
        try {
            setLoading(true);
            setError(null);

            const params = {
                subjectCode: selectedSubject,
                academicYear: classData.academicYear,
                semester: classData.semester,
                branch: classData.branch,
                section: classData.section
            };

            const response = await api.get(`/api/copo-assessment/${facultyId}`, { params });
            setCalculationData(response.data);
        } catch (err) {
            if (err.response?.status === 404) {
                setCalculationData(null);
                setError('No CO-PO assessment found. Please calculate first.');
            } else {
                setError('Failed to fetch CO-PO assessment data');
                console.error('Error fetching calculation data:', err);
            }
        } finally {
            setLoading(false);
        }
    };

    const handleCalculate = async () => {
        if (!selectedSubject || !classData) {
            setError('Please select a subject and ensure class data is available');
            return;
        }

        try {
            setLoading(true);
            setError(null);

            const requestData = {
                facultyId: facultyId,
                subjectCode: selectedSubject,
                academicYear: classData.academicYear,
                semester: classData.semester,
                branch: classData.branch,
                section: classData.section
            };

            const response = await api.post('/api/copo-assessment/calculate', requestData);
            setCalculationData(response.data.data);
            
            // Show success message
            alert('CO-PO assessment calculated successfully!');
        } catch (err) {
            setError(err.response?.data?.details || 'Failed to calculate CO-PO assessment');
            console.error('Error calculating CO-PO assessment:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleGenerateAnalysis = async () => {
        try {
            setLoading(true);
            setError(null);

            const filters = {
                subjectCode: selectedSubject,
                academicYear: classData.academicYear,
                semester: classData.semester,
                branch: classData.branch,
                section: classData.section
            };

            const response = await api.get(`/api/copo-assessment/analysis/${facultyId}`, { params: filters });
            setAnalysisData(response.data.data);
        } catch (err) {
            setError('Failed to generate analysis report');
            console.error('Error generating analysis:', err);
        } finally {
            setLoading(false);
        }
    };

    const getAttainmentLevelBadge = (level) => {
        const levels = {
            0: { label: 'Not Attained', variant: 'destructive' },
            1: { label: 'Partially Attained', variant: 'secondary' },
            2: { label: 'Substantially Attained', variant: 'default' },
            3: { label: 'Fully Attained', variant: 'success' }
        };
        
        const levelInfo = levels[level] || levels[0];
        return <Badge variant={levelInfo.variant}>{levelInfo.label}</Badge>;
    };

    const renderCoAttainments = () => {
        if (!calculationData?.coAttainments) return null;

        return (
            <div className="space-y-4">
                <h3 className="text-lg font-semibold">Course Outcome Attainments</h3>
                <div className="grid gap-4">
                    {calculationData.coAttainments.map((co, index) => (
                        <Card key={index} className="p-4">
                            <div className="flex justify-between items-center mb-2">
                                <h4 className="font-medium">{co.coIdentifier}</h4>
                                {getAttainmentLevelBadge(co.overallAttainment.attainmentLevel)}
                            </div>
                            <div className="grid grid-cols-3 gap-4 text-sm">
                                <div>
                                    <span className="font-medium">Direct Assessment:</span>
                                    <div>{co.directAssessment.percentage.toFixed(1)}%</div>
                                </div>
                                <div>
                                    <span className="font-medium">Indirect Assessment:</span>
                                    <div>{co.indirectAssessment.percentage.toFixed(1)}%</div>
                                </div>
                                <div>
                                    <span className="font-medium">Overall:</span>
                                    <div className="font-semibold">
                                        {co.overallAttainment.weightedPercentage.toFixed(1)}%
                                    </div>
                                </div>
                            </div>
                        </Card>
                    ))}
                </div>
            </div>
        );
    };

    const renderPoAttainments = () => {
        if (!calculationData?.poAttainments) return null;

        return (
            <div className="space-y-4">
                <h3 className="text-lg font-semibold">Program Outcome Attainments</h3>
                <div className="grid gap-4">
                    {calculationData.poAttainments.map((po, index) => (
                        <Card key={index} className="p-4">
                            <div className="flex justify-between items-center mb-2">
                                <h4 className="font-medium">{po.poIdentifier}</h4>
                                {getAttainmentLevelBadge(po.attainmentLevel)}
                            </div>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span className="font-medium">Attainment:</span>
                                    <div className="font-semibold">
                                        {po.attainmentPercentage.toFixed(1)}%
                                    </div>
                                </div>
                                <div>
                                    <span className="font-medium">Mapped COs:</span>
                                    <div>{po.mappedCOs.length}</div>
                                </div>
                            </div>
                            <div className="mt-2">
                                <span className="text-xs text-gray-600">
                                    Mapped COs: {po.mappedCOs.map(co => co.coIdentifier).join(', ')}
                                </span>
                            </div>
                        </Card>
                    ))}
                </div>
            </div>
        );
    };

    const renderOverallStatistics = () => {
        if (!calculationData?.overallStatistics) return null;

        const stats = calculationData.overallStatistics;
        
        return (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card className="p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">{stats.totalCOs}</div>
                    <div className="text-sm text-gray-600">Total COs</div>
                </Card>
                <Card className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">{stats.attainedCOs}</div>
                    <div className="text-sm text-gray-600">Attained COs</div>
                </Card>
                <Card className="p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">{stats.totalPOs}</div>
                    <div className="text-sm text-gray-600">Total POs</div>
                </Card>
                <Card className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">{stats.attainedPOs}</div>
                    <div className="text-sm text-gray-600">Attained POs</div>
                </Card>
            </div>
        );
    };

    const renderAnalysisSummary = () => {
        if (!analysisData?.executiveSummary) return null;

        const summary = analysisData.executiveSummary;
        
        return (
            <div className="space-y-4">
                <h3 className="text-lg font-semibold">Executive Summary</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card className="p-4">
                        <h4 className="font-medium mb-2">Overall Performance</h4>
                        <div className="space-y-2">
                            <div className="flex justify-between">
                                <span>CO Attainment Rate:</span>
                                <span className="font-semibold">
                                    {summary.overallPerformance.coAttainmentRate}%
                                </span>
                            </div>
                            <div className="flex justify-between">
                                <span>PO Attainment Rate:</span>
                                <span className="font-semibold">
                                    {summary.overallPerformance.poAttainmentRate}%
                                </span>
                            </div>
                            <div className="flex justify-between">
                                <span>Performance Grade:</span>
                                <Badge variant="outline">
                                    {summary.overallPerformance.performanceGrade}
                                </Badge>
                            </div>
                        </div>
                    </Card>
                    <Card className="p-4">
                        <h4 className="font-medium mb-2">Key Insights</h4>
                        <ul className="space-y-1 text-sm">
                            {summary.keyInsights.map((insight, index) => (
                                <li key={index} className="flex items-start">
                                    <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                                    {insight}
                                </li>
                            ))}
                        </ul>
                    </Card>
                </div>
            </div>
        );
    };

    return (
        <div className="space-y-6">
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Calculator className="h-5 w-5" />
                        CO-PO Assessment Calculation
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        {/* Subject Selection */}
                        <div className="flex gap-4 items-end">
                            <div className="flex-1">
                                <label className="block text-sm font-medium mb-2">
                                    Select Subject
                                </label>
                                <select
                                    value={selectedSubject}
                                    onChange={(e) => setSelectedSubject(e.target.value)}
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                >
                                    <option value="">Select a subject...</option>
                                    {availableSubjects.map((subject) => (
                                        <option key={subject.code} value={subject.code}>
                                            {subject.code} - {subject.name}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div className="flex gap-2">
                                <Button 
                                    onClick={handleCalculate}
                                    disabled={loading || !selectedSubject}
                                    className="flex items-center gap-2"
                                >
                                    <Calculator className="h-4 w-4" />
                                    Calculate
                                </Button>
                                <Button 
                                    onClick={handleGenerateAnalysis}
                                    disabled={loading || !calculationData}
                                    variant="outline"
                                    className="flex items-center gap-2"
                                >
                                    <TrendingUp className="h-4 w-4" />
                                    Analyze
                                </Button>
                            </div>
                        </div>

                        {/* Error Display */}
                        {error && (
                            <Alert variant="destructive">
                                <AlertDescription>{error}</AlertDescription>
                            </Alert>
                        )}

                        {/* Loading State */}
                        {loading && (
                            <div className="text-center py-8">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                                <p className="mt-2 text-gray-600">Processing...</p>
                            </div>
                        )}

                        {/* Results */}
                        {calculationData && (
                            <Tabs defaultValue="overview" className="w-full">
                                <TabsList className="grid w-full grid-cols-4">
                                    <TabsTrigger value="overview">Overview</TabsTrigger>
                                    <TabsTrigger value="co-attainment">CO Attainment</TabsTrigger>
                                    <TabsTrigger value="po-attainment">PO Attainment</TabsTrigger>
                                    <TabsTrigger value="analysis">Analysis</TabsTrigger>
                                </TabsList>
                                
                                <TabsContent value="overview" className="space-y-4">
                                    <h3 className="text-lg font-semibold">Assessment Overview</h3>
                                    {renderOverallStatistics()}
                                    <div className="text-sm text-gray-600">
                                        Last calculated: {new Date(calculationData.calculationMetadata.lastCalculated).toLocaleString()}
                                    </div>
                                </TabsContent>
                                
                                <TabsContent value="co-attainment">
                                    {renderCoAttainments()}
                                </TabsContent>
                                
                                <TabsContent value="po-attainment">
                                    {renderPoAttainments()}
                                </TabsContent>
                                
                                <TabsContent value="analysis">
                                    {analysisData ? renderAnalysisSummary() : (
                                        <div className="text-center py-8">
                                            <p className="text-gray-600 mb-4">
                                                Generate analysis report to view detailed insights
                                            </p>
                                            <Button onClick={handleGenerateAnalysis} disabled={loading}>
                                                Generate Analysis
                                            </Button>
                                        </div>
                                    )}
                                </TabsContent>
                            </Tabs>
                        )}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
};

export default CoPoCalculation;
