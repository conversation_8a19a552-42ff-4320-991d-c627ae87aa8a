const Assessment = require('../models/Assessment');
const CoPoMapping = require('../models/CoPoMapping');
const CoPoAssessment = require('../models/CoPoAssessment');

class CoPoCalculationService {
    constructor() {
        // Standard CO-PO calculation configuration
        this.config = {
            directAssessmentWeight: 80, // 80%
            indirectAssessmentWeight: 20, // 20%
            attainmentThreshold: 60, // 60% threshold for attainment
            coThresholds: {
                level1: 40, // Level 1: 40-59%
                level2: 60, // Level 2: 60-79%
                level3: 80  // Level 3: 80%+
            }
        };
    }

    /**
     * Calculate CO attainment level based on percentage
     */
    calculateAttainmentLevel(percentage) {
        if (percentage >= this.config.coThresholds.level3) return 3;
        if (percentage >= this.config.coThresholds.level2) return 2;
        if (percentage >= this.config.coThresholds.level1) return 1;
        return 0;
    }

    /**
     * Extract CO-wise data from assessment
     */
    async extractCoDataFromAssessment(assessment) {
        const coData = {};
        
        // Process students data based on assessment type
        assessment.students.forEach(student => {
            if (assessment.type === 'tms') {
                student.tmsMarks.forEach(tmsEntry => {
                    tmsEntry.questions.forEach(question => {
                        ['partA', 'partB'].forEach(part => {
                            if (question[part] && question[part].coNumber) {
                                const coId = question[part].coNumber;
                                if (!coData[coId]) {
                                    coData[coId] = {
                                        coIdentifier: coId,
                                        totalMarks: 0,
                                        studentsData: []
                                    };
                                }
                                coData[coId].totalMarks += question[part].maxMarks || 0;
                                coData[coId].studentsData.push({
                                    rollNo: student.rollNo,
                                    name: student.name,
                                    marksObtained: question[part].marksObtained || 0,
                                    maxMarks: question[part].maxMarks || 0
                                });
                            }
                        });
                    });
                });
            } else if (assessment.type === 'tes') {
                student.tesMarks.forEach(tesEntry => {
                    tesEntry.questions.forEach(question => {
                        ['partA', 'partB', 'partC'].forEach(part => {
                            if (question[part] && question[part].coNumber) {
                                const coId = question[part].coNumber;
                                if (!coData[coId]) {
                                    coData[coId] = {
                                        coIdentifier: coId,
                                        totalMarks: 0,
                                        studentsData: []
                                    };
                                }
                                coData[coId].totalMarks += question[part].maxMarks || 0;
                                coData[coId].studentsData.push({
                                    rollNo: student.rollNo,
                                    name: student.name,
                                    marksObtained: question[part].marksObtained || 0,
                                    maxMarks: question[part].maxMarks || 0
                                });
                            }
                        });
                    });
                });
            } else if (assessment.type === 'tca') {
                student.tcaMarks.forEach(tcaEntry => {
                    if (tcaEntry.marks) {
                        for (const [partKey, partData] of tcaEntry.marks.entries()) {
                            if (partData.coDistribution) {
                                for (const [coId, marks] of Object.entries(partData.coDistribution)) {
                                    const coIdentifier = coId.toUpperCase();
                                    if (!coData[coIdentifier]) {
                                        coData[coIdentifier] = {
                                            coIdentifier: coIdentifier,
                                            totalMarks: 0,
                                            studentsData: []
                                        };
                                    }
                                    coData[coIdentifier].totalMarks += marks || 0;
                                    coData[coIdentifier].studentsData.push({
                                        rollNo: student.rollNo,
                                        name: student.name,
                                        marksObtained: marks || 0,
                                        maxMarks: marks || 0
                                    });
                                }
                            }
                        }
                    }
                });
            }
        });

        return Object.values(coData);
    }

    /**
     * Calculate CO statistics and attainment
     */
    calculateCoStatistics(coData, totalStudents) {
        return coData.map(co => {
            // Aggregate student data by CO
            const studentTotals = {};
            co.studentsData.forEach(data => {
                if (!studentTotals[data.rollNo]) {
                    studentTotals[data.rollNo] = {
                        rollNo: data.rollNo,
                        name: data.name,
                        totalMarks: 0,
                        maxMarks: 0
                    };
                }
                studentTotals[data.rollNo].totalMarks += data.marksObtained;
                studentTotals[data.rollNo].maxMarks += data.maxMarks;
            });

            const aggregatedStudents = Object.values(studentTotals).map(student => ({
                ...student,
                percentage: student.maxMarks > 0 ? (student.totalMarks / student.maxMarks) * 100 : 0
            }));

            // Calculate statistics
            const totalMarks = aggregatedStudents.reduce((sum, s) => sum + s.totalMarks, 0);
            const maxPossibleMarks = aggregatedStudents.reduce((sum, s) => sum + s.maxMarks, 0);
            const averagePercentage = aggregatedStudents.length > 0 
                ? aggregatedStudents.reduce((sum, s) => sum + s.percentage, 0) / aggregatedStudents.length 
                : 0;
            
            const studentsAboveThreshold = aggregatedStudents.filter(s => s.percentage >= this.config.attainmentThreshold).length;
            const attainmentPercentage = totalStudents > 0 ? (studentsAboveThreshold / totalStudents) * 100 : 0;

            return {
                ...co,
                studentsData: aggregatedStudents,
                statistics: {
                    averageMarks: aggregatedStudents.length > 0 ? totalMarks / aggregatedStudents.length : 0,
                    averagePercentage: averagePercentage,
                    studentsAboveThreshold: studentsAboveThreshold,
                    attainmentPercentage: attainmentPercentage
                }
            };
        });
    }

    /**
     * Calculate overall CO attainment
     */
    calculateCoAttainment(directData, indirectData = null) {
        const directPercentage = directData.statistics.attainmentPercentage;
        const indirectPercentage = indirectData ? indirectData.statistics.attainmentPercentage : directPercentage;
        
        // Standard formula: 80% Direct + 20% Indirect
        const weightedPercentage = (directPercentage * this.config.directAssessmentWeight / 100) + 
                                 (indirectPercentage * this.config.indirectAssessmentWeight / 100);
        
        const attainmentLevel = this.calculateAttainmentLevel(weightedPercentage);
        const isAttained = weightedPercentage >= this.config.attainmentThreshold;

        return {
            coIdentifier: directData.coIdentifier,
            directAssessment: {
                totalMarks: directData.studentsData.reduce((sum, s) => sum + s.maxMarks, 0),
                obtainedMarks: directData.studentsData.reduce((sum, s) => sum + s.totalMarks, 0),
                percentage: directPercentage,
                attainmentLevel: this.calculateAttainmentLevel(directPercentage)
            },
            indirectAssessment: indirectData ? {
                totalMarks: indirectData.studentsData.reduce((sum, s) => sum + s.maxMarks, 0),
                obtainedMarks: indirectData.studentsData.reduce((sum, s) => sum + s.totalMarks, 0),
                percentage: indirectPercentage,
                attainmentLevel: this.calculateAttainmentLevel(indirectPercentage)
            } : {
                totalMarks: 0,
                obtainedMarks: 0,
                percentage: directPercentage, // Use direct assessment as fallback
                attainmentLevel: this.calculateAttainmentLevel(directPercentage)
            },
            overallAttainment: {
                weightedPercentage: weightedPercentage,
                attainmentLevel: attainmentLevel,
                isAttained: isAttained
            }
        };
    }

    /**
     * Calculate PO attainment based on CO-PO mapping
     */
    async calculatePoAttainment(coAttainments, coPoMapping) {
        const poAttainments = [];
        
        // Get all PO/PSO identifiers from mapping
        const poIdentifiers = ['po1', 'po2', 'po3', 'po4', 'po5', 'po6', 'po7', 'po8', 'po9', 'po10', 'po11', 'po12', 'pso1', 'pso2', 'pso3', 'pso4'];
        
        poIdentifiers.forEach(poId => {
            const mappedCOs = [];
            let totalWeightedSum = 0;
            let totalMappingStrength = 0;
            
            // Find all COs mapped to this PO
            coPoMapping.courseOutcomes.forEach(coMapping => {
                const mappingStrength = coMapping[poId] || 0;
                if (mappingStrength > 0) {
                    const coAttainment = coAttainments.find(ca => ca.coIdentifier === coMapping.coIdentifier);
                    if (coAttainment) {
                        const weightedContribution = mappingStrength * coAttainment.overallAttainment.attainmentLevel;
                        mappedCOs.push({
                            coIdentifier: coMapping.coIdentifier,
                            mappingStrength: mappingStrength,
                            coAttainment: coAttainment.overallAttainment.attainmentLevel,
                            weightedContribution: weightedContribution
                        });
                        totalWeightedSum += weightedContribution;
                        totalMappingStrength += mappingStrength;
                    }
                }
            });
            
            // Calculate PO attainment
            const attainmentPercentage = totalMappingStrength > 0 ? (totalWeightedSum / (totalMappingStrength * 3)) * 100 : 0;
            const attainmentLevel = this.calculateAttainmentLevel(attainmentPercentage);
            const isAttained = attainmentPercentage >= this.config.attainmentThreshold;
            
            if (mappedCOs.length > 0) {
                poAttainments.push({
                    poIdentifier: poId.toUpperCase(),
                    mappedCOs: mappedCOs,
                    totalWeightedSum: totalWeightedSum,
                    totalMappingStrength: totalMappingStrength,
                    attainmentPercentage: attainmentPercentage,
                    attainmentLevel: attainmentLevel,
                    isAttained: isAttained
                });
            }
        });
        
        return poAttainments;
    }

    /**
     * Main method to calculate CO-PO assessment for a subject
     */
    async calculateCoPoAssessment(facultyId, subjectCode, academicYear, semester, branch, section) {
        try {
            // Fetch all assessments for the subject
            const assessments = await Assessment.find({
                facultyId: facultyId,
                'subject.code': subjectCode,
                academicYear: academicYear,
                semester: semester,
                branch: branch,
                section: section
            });

            if (assessments.length === 0) {
                throw new Error('No assessments found for the specified criteria');
            }

            // Fetch CO-PO mapping
            const coPoMapping = await CoPoMapping.findOne({
                subjectCode: subjectCode,
                academicYear: academicYear,
                semester: semester,
                branch: branch,
                section: section
            });

            if (!coPoMapping) {
                throw new Error('CO-PO mapping not found for the specified criteria');
            }

            // Process each assessment
            const assessmentData = [];
            const allCoData = {};

            for (const assessment of assessments) {
                const coData = await this.extractCoDataFromAssessment(assessment);
                const coStatistics = this.calculateCoStatistics(coData, assessment.numberOfStudents);

                assessmentData.push({
                    assessmentType: assessment.type,
                    assessmentDetails: {
                        tmsType: assessment.tmsType,
                        assessmentNumber: assessment.assessmentNumber,
                        totalStudents: assessment.numberOfStudents,
                        studentsAppeared: assessment.students.length
                    },
                    coWiseData: coStatistics
                });

                // Aggregate CO data across assessments
                coStatistics.forEach(coStat => {
                    if (!allCoData[coStat.coIdentifier]) {
                        allCoData[coStat.coIdentifier] = [];
                    }
                    allCoData[coStat.coIdentifier].push(coStat);
                });
            }

            // Calculate CO attainments
            const coAttainments = [];
            for (const [coId, coDataArray] of Object.entries(allCoData)) {
                // For simplicity, use the first assessment as direct and others as indirect
                // In practice, you might want more sophisticated aggregation
                const directData = coDataArray[0];
                const indirectData = coDataArray.length > 1 ? coDataArray[1] : null;

                const coAttainment = this.calculateCoAttainment(directData, indirectData);
                coAttainments.push(coAttainment);
            }

            // Calculate PO attainments
            const poAttainments = await this.calculatePoAttainment(coAttainments, coPoMapping);

            // Calculate overall statistics
            const totalCOs = coAttainments.length;
            const attainedCOs = coAttainments.filter(co => co.overallAttainment.isAttained).length;
            const coAttainmentPercentage = totalCOs > 0 ? (attainedCOs / totalCOs) * 100 : 0;

            const totalPOs = poAttainments.length;
            const attainedPOs = poAttainments.filter(po => po.isAttained).length;
            const poAttainmentPercentage = totalPOs > 0 ? (attainedPOs / totalPOs) * 100 : 0;

            // Create or update CO-PO assessment record
            const filter = {
                subjectCode: subjectCode,
                academicYear: academicYear,
                semester: semester,
                branch: branch,
                section: section
            };

            const update = {
                facultyId: facultyId,
                subjectName: assessments[0].subject.name,
                assessmentData: assessmentData,
                coAttainments: coAttainments,
                poAttainments: poAttainments,
                overallStatistics: {
                    totalCOs: totalCOs,
                    attainedCOs: attainedCOs,
                    coAttainmentPercentage: coAttainmentPercentage,
                    totalPOs: totalPOs,
                    attainedPOs: attainedPOs,
                    poAttainmentPercentage: poAttainmentPercentage
                },
                calculationMetadata: {
                    lastCalculated: new Date(),
                    calculationMethod: 'standard',
                    dataSource: assessments.map(a => a._id.toString()),
                    isFinalized: false
                }
            };

            const options = { upsert: true, new: true, setDefaultsOnInsert: true };
            const result = await CoPoAssessment.findOneAndUpdate(filter, update, options);

            return result;

        } catch (error) {
            console.error('Error in CO-PO calculation:', error);
            throw error;
        }
    }

    /**
     * Generate CO-PO correlation matrix
     */
    generateCorrelationMatrix(coAttainments, poAttainments, coPoMapping) {
        const matrix = {
            headers: {
                cos: coAttainments.map(co => co.coIdentifier),
                pos: poAttainments.map(po => po.poIdentifier)
            },
            data: []
        };

        coAttainments.forEach(co => {
            const row = {
                coIdentifier: co.coIdentifier,
                coAttainment: co.overallAttainment.attainmentLevel,
                poMappings: {}
            };

            poAttainments.forEach(po => {
                const mapping = coPoMapping.courseOutcomes.find(cm => cm.coIdentifier === co.coIdentifier);
                const mappingStrength = mapping ? mapping[po.poIdentifier.toLowerCase()] || 0 : 0;
                const correlation = mappingStrength * co.overallAttainment.attainmentLevel;

                row.poMappings[po.poIdentifier] = {
                    mappingStrength: mappingStrength,
                    coAttainment: co.overallAttainment.attainmentLevel,
                    correlation: correlation
                };
            });

            matrix.data.push(row);
        });

        return matrix;
    }
}

module.exports = new CoPoCalculationService();
