import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from "@/Components/ui/card";
import { But<PERSON> } from "@/Components/ui/button";
import { Alert, AlertDescription } from "@/Components/ui/alert";
import { Badge } from "@/Components/ui/badge";
import { Grid, Download, Eye } from 'lucide-react';
import api from '../../../utils/api';

const CoPoCorrelationMatrix = ({ facultyId, classData, selectedSubject }) => {
    const [loading, setLoading] = useState(false);
    const [matrixData, setMatrixData] = useState(null);
    const [error, setError] = useState(null);

    useEffect(() => {
        if (selectedSubject && classData) {
            fetchCorrelationMatrix();
        }
    }, [selectedSubject, classData]);

    const fetchCorrelationMatrix = async () => {
        try {
            setLoading(true);
            setError(null);

            const params = {
                subjectCode: selectedSubject,
                academicYear: classData.academicYear,
                semester: classData.semester,
                branch: classData.branch,
                section: classData.section
            };

            const response = await api.get(`/api/copo-assessment/correlation-matrix/${facultyId}`, { params });
            setMatrixData(response.data);
        } catch (err) {
            if (err.response?.status === 404) {
                setError('No CO-PO assessment found. Please calculate the assessment first.');
            } else {
                setError('Failed to fetch correlation matrix');
            }
            console.error('Error fetching correlation matrix:', err);
        } finally {
            setLoading(false);
        }
    };

    const getCorrelationColor = (correlation) => {
        if (correlation >= 9) return 'bg-green-600 text-white';
        if (correlation >= 6) return 'bg-green-400 text-white';
        if (correlation >= 3) return 'bg-yellow-400 text-black';
        if (correlation > 0) return 'bg-orange-400 text-white';
        return 'bg-gray-200 text-gray-600';
    };

    const getCorrelationIntensity = (correlation) => {
        const maxCorrelation = 9; // 3 (max mapping) * 3 (max attainment)
        return Math.min((correlation / maxCorrelation) * 100, 100);
    };

    const renderMatrix = () => {
        if (!matrixData?.data) return null;

        const matrix = matrixData.data;
        const poHeaders = matrix.headers?.pos || [];
        const coData = matrix.data || [];

        return (
            <div className="overflow-x-auto">
                <table className="min-w-full border-collapse border border-gray-300">
                    <thead>
                        <tr className="bg-gray-100">
                            <th className="border border-gray-300 p-2 text-left font-semibold sticky left-0 bg-gray-100 z-10">
                                Course Outcome
                            </th>
                            <th className="border border-gray-300 p-2 text-center font-semibold">
                                CO Attainment
                            </th>
                            {poHeaders.map(po => (
                                <th key={po} className="border border-gray-300 p-2 text-center font-semibold min-w-[80px]">
                                    {po}
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody>
                        {coData.map((co, index) => (
                            <tr key={index} className="hover:bg-gray-50">
                                <td className="border border-gray-300 p-2 font-medium sticky left-0 bg-white z-10">
                                    {co.coIdentifier}
                                </td>
                                <td className="border border-gray-300 p-2 text-center">
                                    <Badge variant={co.coAttainment >= 2 ? 'success' : 'secondary'}>
                                        Level {co.coAttainment}
                                    </Badge>
                                </td>
                                {poHeaders.map(po => {
                                    const mapping = co.poMappings?.[po];
                                    const correlation = mapping?.correlation || 0;
                                    const mappingStrength = mapping?.mappingStrength || 0;
                                    
                                    return (
                                        <td 
                                            key={`${co.coIdentifier}-${po}`} 
                                            className="border border-gray-300 p-1 text-center relative"
                                        >
                                            <div 
                                                className={`p-2 rounded text-sm font-medium ${getCorrelationColor(correlation)}`}
                                                title={`Mapping: ${mappingStrength}, CO Attainment: ${co.coAttainment}, Correlation: ${correlation}`}
                                            >
                                                {correlation.toFixed(1)}
                                            </div>
                                            {mappingStrength > 0 && (
                                                <div className="text-xs text-gray-500 mt-1">
                                                    M: {mappingStrength}
                                                </div>
                                            )}
                                        </td>
                                    );
                                })}
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        );
    };

    const renderLegend = () => (
        <Card className="mt-4">
            <CardHeader>
                <CardTitle className="text-sm">Legend</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div className="space-y-2">
                        <h4 className="font-medium">Correlation Levels</h4>
                        <div className="space-y-1">
                            <div className="flex items-center gap-2">
                                <div className="w-4 h-4 bg-green-600 rounded"></div>
                                <span>High (≥9)</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-4 h-4 bg-green-400 rounded"></div>
                                <span>Good (6-8)</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-4 h-4 bg-yellow-400 rounded"></div>
                                <span>Medium (3-5)</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-4 h-4 bg-orange-400 rounded"></div>
                                <span>Low (1-2)</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-4 h-4 bg-gray-200 rounded"></div>
                                <span>None (0)</span>
                            </div>
                        </div>
                    </div>
                    <div className="space-y-2">
                        <h4 className="font-medium">Mapping Strength</h4>
                        <div className="space-y-1 text-xs">
                            <div>1 = Low correlation</div>
                            <div>2 = Medium correlation</div>
                            <div>3 = High correlation</div>
                            <div>M: Shows mapping strength</div>
                        </div>
                    </div>
                    <div className="space-y-2">
                        <h4 className="font-medium">CO Attainment Levels</h4>
                        <div className="space-y-1 text-xs">
                            <div>0 = Not Attained (&lt;40%)</div>
                            <div>1 = Partially Attained (40-59%)</div>
                            <div>2 = Substantially Attained (60-79%)</div>
                            <div>3 = Fully Attained (≥80%)</div>
                        </div>
                    </div>
                    <div className="space-y-2">
                        <h4 className="font-medium">Calculation</h4>
                        <div className="space-y-1 text-xs">
                            <div>Correlation = Mapping Strength × CO Attainment Level</div>
                            <div>Maximum possible correlation = 9</div>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );

    const renderStatistics = () => {
        if (!matrixData?.data) return null;

        const matrix = matrixData.data;
        const totalCorrelations = matrix.data.reduce((total, co) => {
            return total + Object.values(co.poMappings || {}).length;
        }, 0);

        const strongCorrelations = matrix.data.reduce((count, co) => {
            return count + Object.values(co.poMappings || {}).filter(mapping => mapping.correlation >= 6).length;
        }, 0);

        const averageCorrelation = matrix.data.reduce((sum, co) => {
            const coCorrelations = Object.values(co.poMappings || {});
            const coSum = coCorrelations.reduce((s, mapping) => s + mapping.correlation, 0);
            return sum + coSum;
        }, 0) / totalCorrelations || 0;

        return (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <Card className="p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">{totalCorrelations}</div>
                    <div className="text-sm text-gray-600">Total Correlations</div>
                </Card>
                <Card className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">{strongCorrelations}</div>
                    <div className="text-sm text-gray-600">Strong Correlations (≥6)</div>
                </Card>
                <Card className="p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600">{averageCorrelation.toFixed(2)}</div>
                    <div className="text-sm text-gray-600">Average Correlation</div>
                </Card>
            </div>
        );
    };

    const handleExportMatrix = () => {
        if (!matrixData) return;

        // Create CSV content
        const matrix = matrixData.data;
        const poHeaders = matrix.headers?.pos || [];
        const coData = matrix.data || [];

        let csvContent = "Course Outcome,CO Attainment," + poHeaders.join(",") + "\n";
        
        coData.forEach(co => {
            let row = `${co.coIdentifier},${co.coAttainment}`;
            poHeaders.forEach(po => {
                const correlation = co.poMappings?.[po]?.correlation || 0;
                row += `,${correlation.toFixed(2)}`;
            });
            csvContent += row + "\n";
        });

        // Download CSV
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `CO-PO_Correlation_Matrix_${selectedSubject}_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    };

    if (!selectedSubject) {
        return (
            <Card>
                <CardContent className="text-center py-8">
                    <Grid className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">Please select a subject to view the correlation matrix</p>
                </CardContent>
            </Card>
        );
    }

    return (
        <div className="space-y-6">
            <Card>
                <CardHeader>
                    <div className="flex justify-between items-center">
                        <CardTitle className="flex items-center gap-2">
                            <Grid className="h-5 w-5" />
                            CO-PO Correlation Matrix
                        </CardTitle>
                        <div className="flex gap-2">
                            <Button 
                                onClick={fetchCorrelationMatrix}
                                disabled={loading}
                                variant="outline"
                                size="sm"
                                className="flex items-center gap-2"
                            >
                                <Eye className="h-4 w-4" />
                                Refresh
                            </Button>
                            <Button 
                                onClick={handleExportMatrix}
                                disabled={!matrixData}
                                variant="outline"
                                size="sm"
                                className="flex items-center gap-2"
                            >
                                <Download className="h-4 w-4" />
                                Export CSV
                            </Button>
                        </div>
                    </div>
                    {matrixData?.metadata && (
                        <div className="text-sm text-gray-600">
                            {matrixData.metadata.subjectName} ({matrixData.metadata.subjectCode}) - 
                            {matrixData.metadata.academicYear}, Semester {matrixData.metadata.semester}
                        </div>
                    )}
                </CardHeader>
                <CardContent>
                    {error && (
                        <Alert variant="destructive" className="mb-4">
                            <AlertDescription>{error}</AlertDescription>
                        </Alert>
                    )}

                    {loading && (
                        <div className="text-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                            <p className="mt-2 text-gray-600">Loading correlation matrix...</p>
                        </div>
                    )}

                    {matrixData && !loading && (
                        <>
                            {renderStatistics()}
                            {renderMatrix()}
                            {renderLegend()}
                        </>
                    )}
                </CardContent>
            </Card>
        </div>
    );
};

export default CoPoCorrelationMatrix;
