# CO-PO Assessment System - Setup Guide

## 🚨 **Issue Resolution: "Class Data Not Available"**

The CO-PO Assessment System is showing "Class Data Not Available" because the faculty user needs to be properly assigned to classes with subjects. Here's how to fix this:

## 🔧 **Solution Steps**

### **Step 1: Assign Faculty to Classes**

The faculty user needs to have class assignments in their user profile. This can be done through the backend API or database directly.

#### **Option A: Using API Endpoint (Recommended)**

Use the existing `/auth/addClass` endpoint to assign classes to the faculty:

```http
POST /auth/addClass
Content-Type: application/json

{
  "userId": "FACULTY_USER_ID",
  "branch": "CSE",
  "section": "A",
  "year": 3,
  "semester": 5,
  "academicYear": "2023-24",
  "subjects": [
    {
      "code": "CS501",
      "name": "Software Engineering"
    },
    {
      "code": "CS502", 
      "name": "Database Management Systems"
    },
    {
      "code": "CS503",
      "name": "Computer Networks"
    }
  ]
}
```

#### **Option B: Direct Database Update**

If you have direct database access, update the User document:

```javascript
// MongoDB update query
db.users.updateOne(
  { _id: ObjectId("FACULTY_USER_ID") },
  {
    $push: {
      classes: {
        branch: "CSE",
        section: "A", 
        year: 3,
        semester: 5,
        academicYear: "2023-24",
        subjects: [
          { code: "CS501", name: "Software Engineering" },
          { code: "CS502", name: "Database Management Systems" },
          { code: "CS503", name: "Computer Networks" }
        ]
      }
    }
  }
)
```

### **Step 2: Verify Class Assignment**

After assigning classes, verify the assignment by checking the user data:

```http
GET /auth/me
Authorization: Bearer YOUR_JWT_TOKEN
```

The response should include a `classes` array with the assigned class information.

### **Step 3: Access CO-PO System**

Once the faculty is properly assigned to classes:

1. **Login** to the faculty portal
2. **Navigate** to `/copo` route
3. **Verify** that class data is now available
4. **Select subjects** from the dropdown to begin CO-PO assessment

## 📋 **Required Data Structure**

For the CO-PO system to work properly, the faculty user document should have this structure:

```javascript
{
  "_id": "faculty_user_id",
  "username": "faculty1",
  "fullname": "Dr. Faculty Name",
  "email": "<EMAIL>",
  "department": "Computer Science",
  "role": "faculty",
  "classes": [
    {
      "branch": "CSE",
      "section": "A",
      "year": 3,
      "semester": 5,
      "academicYear": "2023-24",
      "subjects": [
        {
          "code": "CS501",
          "name": "Software Engineering",
          "assessments": [] // Will be populated when assessments are created
        },
        {
          "code": "CS502", 
          "name": "Database Management Systems",
          "assessments": []
        }
      ]
    }
  ]
}
```

## 🎯 **Quick Setup Script**

Here's a Node.js script to quickly set up a faculty with class assignments:

```javascript
// setupFaculty.js
const mongoose = require('mongoose');
const User = require('./models/User');

async function setupFacultyClasses() {
    try {
        await mongoose.connect('mongodb://localhost:27017/ns_acad');
        
        // Replace with actual faculty username or ID
        const facultyUsername = 'faculty1';
        
        const faculty = await User.findOne({ username: facultyUsername });
        if (!faculty) {
            console.log('Faculty not found');
            return;
        }
        
        // Add class assignment
        faculty.classes.push({
            branch: 'CSE',
            section: 'A',
            year: 3,
            semester: 5,
            academicYear: '2023-24',
            subjects: [
                { code: 'CS501', name: 'Software Engineering' },
                { code: 'CS502', name: 'Database Management Systems' },
                { code: 'CS503', name: 'Computer Networks' }
            ]
        });
        
        await faculty.save();
        console.log('Faculty class assignment completed!');
        
    } catch (error) {
        console.error('Error:', error);
    } finally {
        await mongoose.disconnect();
    }
}

setupFacultyClasses();
```

## 🔍 **Troubleshooting**

### **Issue: Still showing "Class Data Not Available"**

1. **Check User Data**: Verify the faculty user has `classes` array populated
2. **Check Token**: Ensure the JWT token is valid and contains correct user ID
3. **Check API Response**: Verify `/auth/me` endpoint returns user data with classes
4. **Clear Cache**: Clear browser cache and localStorage
5. **Check Console**: Look for any JavaScript errors in browser console

### **Issue: Subjects not showing in dropdown**

1. **Verify Subject Structure**: Ensure subjects have both `code` and `name` fields
2. **Check Array Format**: Subjects should be an array of objects
3. **Validate Data**: Ensure no null or undefined values in subjects array

### **Issue: CO-PO calculations not working**

1. **Create Assessments**: Ensure assessment data exists for the subjects
2. **Set CO-PO Mappings**: Configure CO-PO mappings for the subjects
3. **Check Permissions**: Verify faculty has access to assessment data

## 📞 **Support**

If you continue to experience issues:

1. **Check Logs**: Review backend server logs for any errors
2. **Verify Database**: Ensure MongoDB is running and accessible
3. **Test API**: Use tools like Postman to test API endpoints directly
4. **Contact Admin**: Reach out to system administrator for user setup

## ✅ **Success Indicators**

You'll know the setup is working when:

- ✅ Faculty login shows class information in dashboard header
- ✅ CO-PO route displays dashboard instead of "Class Data Not Available"
- ✅ Subject dropdown shows assigned subjects
- ✅ Assessment calculation works without errors

---

## 🎉 **Next Steps After Setup**

Once class data is available:

1. **Configure CO-PO Mappings** for your subjects
2. **Create Assessment Data** (TMS, TCA, TES)
3. **Calculate CO-PO Assessments** using the dashboard
4. **Generate Reports** and correlation matrices
5. **Export Data** for accreditation purposes

The CO-PO Assessment System is now ready for full use!
